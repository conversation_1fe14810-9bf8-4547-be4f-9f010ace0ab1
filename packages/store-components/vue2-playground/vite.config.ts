import autoprefixer from "autoprefixer";
import { resolve } from "path";
import ScriptSetup from "unplugin-vue2-script-setup/vite";
import { defineConfig } from "vite";
import { createVuePlugin as vue2 } from "vite-plugin-vue2";
import {
  defaultPlugins,
  entryFiles,
  entryLists,
  globalThisPatch,
  isCos
} from "../vite.base.config";
const target = 'https://appilvzothq7546.xet.citv.cn/'; // 代理访问店铺，本地测试模式下使用
const c_user_token = '9b9f640f240911e275c14bedd5732130';
const realTarget = target.endsWith('/') ? target.substring(0, target.length - 1) : target;

export default defineConfig(() => {
  return {
    define: {
      // 解决umd打包后，在浏览器端无法处理的语法和变量，需要先转义
      'process.env.NODE_ENV': JSON.stringify('production'),
      "require('vue')": 'window.Vue'
    },
    plugins: [vue2(), ScriptSetup({}), ...defaultPlugins],
    server: {
      host: "0.0.0.0",
      port: 2000,
      cors: true,
      proxy: {
        "^(/scrm.clues.manage|/wework|/xe)": {
          target: target,//"https://admin.xiaoe-tech.com", // 替换的服务器地址
          changeOrigin: true, // 开启代理，允许跨域
          headers: {
            cookie: `ko_token=${c_user_token}`,
            'x-forwarded-host': realTarget.includes('https://')
            ? realTarget.substr('https://'.length)
            : realTarget.substr('http://'.length),
           'x-forwarded-port': realTarget.includes('https://') ? '443' : '80',
          },
        }
      },
    },
    resolve: {
      alias: {
        "@": resolve(__dirname, "../src"),
        vue: resolve(__dirname, "./node_modules/vue/dist/vue.runtime.esm.js"),
        'dayjs': resolve(__dirname, '../node_modules/dayjs/esm/index.js'),
        "vue-demi": resolve(
          __dirname,
          "../node_modules/vue-demi/lib/v2/index.mjs"
        ),
        '@vue/composition-api/dist/vue-composition-api.mjs': resolve(
          __dirname,
          './node_modules/@vue/composition-api/dist/vue-composition-api.mjs'
        ),
        'vant/es': resolve(__dirname, '../node_modules/vant-v2/es'),
        "vant": resolve(__dirname, '../node_modules/vant-v2/es/index.js'),
        "vue-lazyload": resolve(__dirname, '../src/utils/lazyload/index.js'),
      },
      extensions: [".mjs", ".js", ".ts", ".json", ".vue", ".scss"],
    },
    build: {
      outDir: resolve(__dirname, `../${isCos() ? 'libCos' : 'lib/v2'}`),
      emptyOutDir: false,
      target: 'es2015',
      lib: {
        entry: isCos() ? entryFiles('v2') : entryLists(),
        formats: isCos() ? ["umd"] : ["es"],
        name: process.env.MODULE_NAME || 'store-components', // umd模式需要设置不同的全局变量
        fileName: (format, entryName) => {
          return `${entryName}/index.js`;
        },
      },
      rollupOptions: {
        external: ["vue", '@vue/composition-api/dist/vue-composition-api.mjs'],
        output: {
          // 在 UMD 构建模式下为这些外部化的依赖提供一个全局变量
          globals: {
            vue: "Vue",
            '@vue/composition-api/dist/vue-composition-api.mjs': 'VueCompositionAPI'
          },
          assetFileNames: `[name]-[hash:6].[ext]`, //除了js以外资源
          banner: `${globalThisPatch}`
        },
      },
    },
    css: {
      postcss: {
        plugins: [
          autoprefixer({
            // 指定目标浏览器
            overrideBrowserslist: [
              "chrome >= 58",
              "ie >= 11",
              "safari >= 8",
              "ios >= 8",
              "android >= 8",
            ],
          }),
        ],
      },
    },
    optimizeDeps: {
      exclude: ["vue-demi", "vue", "vue2"],
    },
  };
});
