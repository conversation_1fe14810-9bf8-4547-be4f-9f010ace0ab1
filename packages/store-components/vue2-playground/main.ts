/*
 * @Author: joeyang <EMAIL>
 * @Date: 2025-05-07 10:22:21
 * @LastEditors: joeyang <EMAIL>
 * @LastEditTime: 2025-05-07 11:52:24
 * @FilePath: /h5-retail-shop-components/packages/store-components/vue2-playground/main.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from "vue";
import App from "./App.vue";
import Vant from '@xiaoe/vant';
import '@xiaoe/vant/lib/index.css';

Vue.use(Vant);
// import router from "./router.ts";
// console.log(router, 666);


Vue.config.productionTip = false;

new Vue({
    // router,
    render: (h) => h(App)
}).$mount("#app");
