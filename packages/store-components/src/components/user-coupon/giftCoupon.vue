<template>
    <div class="gift-coupon-wrap">
        <div class="gift-coupon" :class="{ disabledState: disabled }">
            <div class="gift-coupon-name">
                <div class="name">礼品券</div>
                <div class="nums-info" v-if="item.count && item.count > 1">
                    <span :class="[longCount()]">x</span>
                    <span :class="['count', longCount()]">{{item.count}}</span>
                    <span :class="['unit', longCount()]" class="unit">张</span>
                </div>
            </div>
            <div class="gift-coupon-info">
                <div class="coupon-info">
                    <div class="goods-name">{{item.goods_name}}</div>
                    <div class="coupon-use-time" v-if="item.min_invalid_at && item.max_invalid_at">
                        有效期：{{ item.min_invalid_at.trim().split(" ")[0] }} 至 {{item.max_invalid_at.trim().split(" ")[0]}}
                    </div>
                </div>
                <div class="coupon-state">
                    <slot name="couponState"></slot>
                </div>
            </div>
            <div class="right-bg"></div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'giftCoupon',
    props: {
        item: {
            // 单个礼品券数据
            type: Object,
            default: () => {
                return {};
            }
        },
        type: {
            type: Number,
            default: 0
        },
        disabled: {
            // 标记礼品券是否可用，默认可用
            type: Boolean,
            default: false
        }
    },
    data() {
        return {

        };
    },
    methods: {
        /** 长数字 */
        longCount() {
            const len = this.item.count?.toString().length || 0
            if(len > 8) {
                return 'f10'
            }else if(len > 4) {
                return 'f12'
            }else if(len > 3){
                return 'f16'
            }else if(len > 2){
                return 'f20'
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.gift-coupon-wrap {
    width: 100%;
    margin-bottom: 0.24rem;
    margin-left: auto;
    margin-right: auto;
    margin-top: 0.32rem;
    &:last-child {
        margin-bottom: 0;
    }

}
.gift-coupon{
    display: flex;
    justify-content: center;
    align-items: center;
    height: 1.76rem;
    position: relative;
    padding: 0 0.32rem;

    .gift-coupon-name{
        width: 2.24rem;
        height: 100%;
        background: url('https://commonresource-1252524126.cdn.xiaoeknow.com/image/lwrjdinb0zh9.png') center no-repeat;
        background-size: 100% 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        box-sizing: border-box;
        color: #FF5429;
        .name{
            padding-left: .36rem;
            padding-bottom: .1rem;
            font-size: .36rem;
            font-weight: 500;
        }
        .nums-info{
            font-size: .32rem;
            text-align: center;
            max-width: 1.8rem;
            overflow: hidden;
            font-weight: 500;
            box-sizing: border-box;
            .count{
                font-size: .56rem;
                font-weight: 600;
                padding: 0 .06rem;
                line-height: .68rem;
            }
            .f20{
                font-size: .4rem !important;
            }
            .f16{
                font-size: .32rem !important;
            }
            .f12{
                font-size: .24rem !important;
            }
            .f10{
                font-size: .2rem !important;
            }
            .unit{
                font-size: .24rem;
                line-height: .32rem;
            }
        }
    }
    .gift-coupon-info{
        width: 5rem;
        height: 100%;
        background: url('https://commonresource-1252524126.cdn.xiaoeknow.com/image/lwrjdin10t67.png') center no-repeat;
        background-size: 100% 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: .14rem 0;
        box-sizing: border-box;
        .coupon-info{
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            height: 100%;
            flex: 1;
            .goods-name{
                font-size: .28rem;
                font-weight: 500;
                line-height: .4rem;
                color: #333333;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                align-self: stretch;
                word-break: break-all;
            }
            .coupon-use-time{
                font-size: .2rem;
                font-weight: 400;
                line-height: .32rem;
                color: #666666;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                align-self: stretch;
            }
        }

        .coupon-state {
            margin-left: .1rem;
        }
    }
    .right-bg{
        width: .16rem;
        height: 100%;
        background: url('https://commonresource-1252524126.cdn.xiaoeknow.com/image/lwrjdinc03xi.png') center no-repeat;
        background-size: 100% 100%;
    }
}

// 礼品券不可用样式
.disabledState {
    .name {
        color:#999
    }
    .goods-name {
        color: #666 !important;
    }
    .nums-info {
        color:#999
    }
    .coupon-use-time {
        color:#999 !important;
    }
    .coupon-name{
        color: #333 !important;
    }
    .coupon-state{
        justify-content: center;
    }
    .gift-coupon-name {
        background: url('https://commonresource-1252524126.cdn.xiaoeknow.com/image/lyqxe2l60tsl.png') center no-repeat !important;
        background-size: 100% 100% !important;
    }
    .gift-coupon-info {
        background: url('https://commonresource-1252524126.cdn.xiaoeknow.com/image/lyqxe2l90j9i.png') center no-repeat !important;
        background-size: 100% 100% !important;
    }
    .right-bg {
        background: url('https://commonresource-1252524126.cdn.xiaoeknow.com/image/lyqxe2la0h85.png') center no-repeat !important;
        background-size: 100% 100% !important;
    }
}
</style>
