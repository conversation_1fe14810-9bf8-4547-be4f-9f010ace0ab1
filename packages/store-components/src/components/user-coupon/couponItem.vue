<template>
    <div class="coupon-wrap">
        <div class="coupon" :class="{ disabledState: disabled }">
            <div class="coupon-content">
                <div class="coupon-value">
                    <div class="coupon-price">
                        <template v-if="item.discount_way === 1">
                            <span class="coupon-symbol">￥</span>
                            <span
                                style="left: -0.05rem"
                                :class="{
                                    fontSmall: isLongNum(item.price) == 'small',
                                    fontLastSmall: isLongNum(item.price) == 'smallLast'
                                }"
                            >
                                <span>{{ item.price | ToFilter }}</span>
                                <span class="coupon-font" v-if="item.price % 100 !== 0">.{{ fixedFont(item.price) }}</span>
                            </span>
                        </template>
                        <span
                            style="left: -0.05rem"
                            :class="{
                                fontSmall: isLongNum(item.price) == 'small',
                                fontLastSmall: isLongNum(item.price) == 'smallLast'
                            }"
                            v-if="item.discount_way === 2"
                        >
                            <span>{{ item.discount_percent | ToFilter(false) }}</span>
                            <span class="coupon-percent" v-if="item.discount_percent % 10 !== 0">
                                .{{ fixedFont(item.discount_percent, false) }}
                            </span>
                            <span class="coupon-discount">折</span>
                        </span>
                    </div>
                    <div class="coupon-condition t3">{{ useCondition(item.require_price ,item.discount_way, item.price )}}</div>
                    <div class="coupon_discount_max" v-if="type === 1 && item.discount_limit_price">
                        最多优惠{{ (item.discount_limit_price / 100).toFixed(2) }}元
                    </div>
                </div>
                <div class="coupon-info-bg">
                    <div
                        class="coupon-info"
                        :class="type === 1 && !disabled ? 'coupon_info-small' : ''"
                    >
                        <div class="coupon-name" :class="{ couponName: item.instructions!= '' && showUseInformation}">
                            {{ item.title }}
                        </div>
                        <div class="use-desc">
                            <div>
                                <span class="coupon-use-time" v-if="!item.valid_day && item.invalid_at">
                                    有效期至：{{ formatDate(item.invalid_at) }}
                                </span>
                                <span class="coupon-use-range">
                                    可适用于：{{item.type == 1 ? '全部商品' : '部分商品'}}
                                </span>
                                <div class="more-info" v-if='item.instructions!= "" && showUseInformation'>
                                    使用说明
                                    <span class="more-info_operate expand" :class="{ retract: is_viewUsingInfo }" @click="handleLook"></span>
                                </div>
                            </div>
                            <div class="coupon-state">
                                <slot name="couponState"></slot>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="right-bg"></div>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="detail-info" v-show=" item.instructions!= '' && is_viewUsingInfo && showUseInformation" :class="{ detailInfoDisabled: disabled }">
            <div class="instructions">
                <span v-html="replaceInstruction(item.instructions)"></span>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'myCoupon',
    props: {
        item: {
            // 单个优惠券数据
            type: Object,
            default: () => {
                return {};
            }
        },
        type: {
            type: Number,
            default: 0
        },
        disabled: {
            // 标记优惠券是否可用，默认可用
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            showUseInformation: true,
            is_viewUsingInfo: false
        };
    },
    created() {
        // this.is_viewUsingInfo = !this.disabled ? true : false;
    },
    computed: {
        useCondition() {
            return function (requirePrice,way,price) {
                return requirePrice == 0 ? '无门槛' : way === 2 ? `满${requirePrice / 100}元可用` : `满${requirePrice / 100}减${price / 100}元`
            }
        },
        // 时间精确到日期
        formatDate() {
            return function (date) {
                let execStr = /\d{4}-\d{1,2}-\d{1,2}/g.exec(date);
                if (execStr && execStr[0]) {
                    return execStr[0].replace(/-/g, '.');
                } else {
                    return date;
                }
            };
        },
        // 时间精确到分
        formatDateToMinte() {
            return function (date) {
                if (date) {
                    let lastIndex = date.lastIndexOf(':');
                    return date.substring(0, lastIndex).replace(/-/g, '.');
                } else {
                    return date;
                }
            };
        },
        // 判断金额长度，不同金额区间端显示不同样式
        isLongNum() {
            return function (price) {
                if (price) {
                    let tempPrice = price / 100;
                    if (price % 100 === 0) {
                        if (tempPrice.toString().length > 4) {
                            return 'smallLast';
                        } else if (tempPrice.toString().length > 3) {
                            return 'small';
                        } else {
                            return 'normal';
                        }
                    } else {
                        // 含小数点
                        if (tempPrice.toString().length > 7) {
                            return 'smallLast';
                        } else if (tempPrice.toString().length > 5) {
                            return 'small';
                        } else {
                            return 'normal';
                        }
                    }
                } else {
                    return 'normal';
                }
            };
        },
        // 小数点后的样式补充 ToFix2-默认保留2位,其他保留1位
        fixedFont() {
            return (value, ToFix2 = true) => {
                if (value) {
                    let result = (value / 100).toString();
                    if (!ToFix2) {
                        result = (value / 10).toString();
                    }
                    if (result.toString().indexOf('.') > -1) {
                        return result.split('.')[1];
                    } else {
                        return '';
                    }
                }
            };
        }
    },
    filters: {
        // 做数值过滤，value-要处理的数字 ToFix2-默认保留两位，默认转分转元，除以100。其他除以10(如折扣)
        ToFilter: (value, ToFix2 = true) => {
            let result = (value / 100).toString();
            if (!ToFix2) {
                result = (value / 10).toString();
            }
            if (result.indexOf('.') > 0) {
                return result.split('.')[0];
            } else {
                return result;
            }
        }
    },
    methods: {
        replaceInstruction(str) {
            if (!str) {
                return '';
            }
            return str.replace(/\r\n/g, '<br/>');
        },
        handleLook() {
            this.is_viewUsingInfo = !this.is_viewUsingInfo;
        },
    }
};
</script>

<style lang="scss" scoped>
.coupon-wrap {
    width: 100%;
    margin-bottom: 0.24rem;
    margin-left: auto;
    margin-right: auto;
    margin-top: 0.32rem;
    &:last-child {
        margin-bottom: 0;
    }
    .detail-info {
        display: flex;
        align-items: center;
        padding: 0.32rem;
        background-color: #fff;
        box-sizing: border-box;
        border-radius: 0 0 0.1rem 0.1rem;
        margin-top: -5px;
        border: 1px solid #e9e9e9;
        border-top: none;
        color: #FFFFFF;
        font-size: 0.2rem;
        text-align: left;
        word-break: break-all;
        .instructions {
            color: #999999;
            span {
                white-space: pre-wrap;
            }
        }
    }
    .detailInfoDisabled {
        border: 1px solid #e9e9e9;
        border-top: none;
    }
}
.coupon{
    position: relative;
    padding: 0 0.32rem;
    height: 1.76rem;
    overflow: hidden;
    .coupon-content {
        display: flex;
        .coupon-value {
            width: 2.24rem;
            height: 1.76rem;
            background: url('https://commonresource-1252524126.cdn.xiaoeknow.com/image/lwrjdinb0zh9.png') center no-repeat;
            background-size: 100% 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            box-sizing: border-box;
            color: #FF5429;
            .coupon-price {
                width: 1.8rem;
                text-align: center;
                line-height: 0.72rem;
                font-size: 0.72rem;
                font-weight: 500;
                .fontSmall {
                    font-size: 0.48rem !important;
                }
                .fontLastSmall {
                    font-size: 0.32rem !important;
                }
                .coupon-font {
                    font-size: 0.24rem !important;
                }
                .coupon-percent {
                    font-size: 0.32rem !important;
                }
                span {
                    position: relative;
                    display: inline-block;
                }
                .coupon-discount,
                .coupon-symbol {
                    font-size: 0.32rem;
                    margin-left: 0.02rem;
                    margin-left: -0.01rem;
                }
            }
            .coupon-condition {
                width: 1.8rem;
                overflow: hidden;
                max-height: 0.8rem;
                line-height: 0.32rem;
                text-align: center;
                font-size: 0.24rem;
            }
            .coupon_discount_max {
                width: 1.8rem;
                text-align: center;
                color: #ff781f;
                font-size: 10px;
            }
        }
        .coupon-info-bg{
            position: relative;
            // width: 5rem;
            width: calc(100% - 2.24rem);
            height: 1.76rem;
            background: url('https://commonresource-1252524126.cdn.xiaoeknow.com/image/lwrjdin10t67.png') center no-repeat;
            background-size: 100% 100%;
            box-sizing: border-box;
            overflow: hidden;
            display: flex;
            align-items: center;
            .coupon-info {
                position: relative;
                color: #333333;
                font-size: 0.2rem;
                font-weight: 400;
                width: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                height: 100%;
                // width: 3.2rem;
                word-break: break-all;
                box-sizing: border-box;

                .use-desc {
                    display: flex; 
                    justify-content: space-between;
                }

                .coupon-name {
                    line-height: 0.32rem;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    margin-bottom: 0.3rem;
                    color: #333333;
                    font-size: 0.28rem;
                    font-weight: 500;
                }
                .coupon-use-time {
                    height: 0.24rem;
                    line-height: 0.24rem;
                    padding-bottom: 0.08rem;
                    display: block;
                }
                .coupon-use-range {
                    display: block;
                }
                .couponName {
                    margin-bottom: 0.16rem;
                }
                .more-info {
                    position: relative;
                    color: #999999;
                    vertical-align: bottom;
                    padding: 0.08rem 0;
                }
                .more-info_operate {
                    width: 0.24rem;
                    height: 0.24rem;
                    display: inline-block;
                    cursor: pointer;
                    position: absolute;
                    margin-top: 0.01rem;
                    margin-left: 0.06rem;
                    background-size: 100% 100%;
                }
                .expand {
                    background: url('https://commonresource-1252524126.cdn.xiaoeknow.com/image/l37do88p077u.svg') center no-repeat;
                }
                .retract {
                    background: url('https://commonresource-1252524126.cdn.xiaoeknow.com/image/l37domok0yr2.svg') center no-repeat;
                }
            }
            .coupon_info-small {
                padding: 0.12rem 0 0.12rem 0.24rem;
            }
            .coupon-state {
                margin-left: .1rem;
            }
        }
        .right-bg{
            width: .16rem;
            height: 1.76rem;
            background: url('https://commonresource-1252524126.cdn.xiaoeknow.com/image/lwrjdinc03xi.png') center no-repeat;
            background-size: 100% 100%;
        }
    }

}
// 优惠券不可用样式
.disabledState {
    .coupon-info {
        // background: #B2B2B2 !important;
        color: #999999 !important;
        .coupon-name {
            color: #666 !important;
        }
    }
    .coupon-price, .coupon-condition{
        color: #999999 !important;
    }
    .coupon-value {
        background: url('https://commonresource-1252524126.cdn.xiaoeknow.com/image/lyqxe2l60tsl.png') center no-repeat !important;
        background-size: 100% 100% !important;
    }
    .coupon-info-bg {
        background: url('https://commonresource-1252524126.cdn.xiaoeknow.com/image/lyqxe2l90j9i.png') center no-repeat !important;
        background-size: 100% 100% !important;
    }
    .right-bg {
        background: url('https://commonresource-1252524126.cdn.xiaoeknow.com/image/lyqxe2la0h85.png') center no-repeat !important;
        background-size: 100% 100% !important;
    }
}

</style>
