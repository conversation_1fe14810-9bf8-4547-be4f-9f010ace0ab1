<template>
  <div class="coupon-center">
      <xiaoe-tabs :line-width="24" @click="changeTab" type="card" v-model="currentTab">
          <xiaoe-tab :title="`未使用(${normal_total})`" :name="1">
                  <couponList :currentTab="currentTab"></couponList>
          </xiaoe-tab>
          <xiaoe-tab :title="`已使用(${used_total})`" :name="2">
              <couponList :currentTab="currentTab"></couponList>
          </xiaoe-tab>
          <xiaoe-tab :title="`已无效(${invalid_total})`" :name="0">
              <couponList :currentTab="currentTab"></couponList>
          </xiaoe-tab>
      </xiaoe-tabs>
  </div>
</template>

<script>
import {getCouponNum} from '@/apis/index'
import couponList from './couponList.vue'

function getQueryStrDecode(key) {
    let reg = new RegExp("(^|&)" + key + "=([^&]*)(&|$)");
    let r = window.location.search.substr(1).match(reg);
    if (r != null) {
        return decodeURI(r[2]);
    }
    return null;
}
export default {
  components: {
      couponList
  },
  data() {
      return {
          currentTab: 1,
          app_id: '',
          customer_user_id: '',
          normal_total: 0, // 未使用优惠券
          used_total: 0, // 已使用优惠券
          invalid_total: 0, // 已过期优惠券
          loading: false,
          hasColdData1: true, // 已使用_是否有冷备数据
          hasColdData2: true, // 已无效_是否有冷备数据
      };
  },
  mounted(){
      this.app_id = getQueryStrDecode('app_id')
      this.customer_user_id = getQueryStrDecode('customer_user_id')
      this.getCouponCount()
  },
  created() {
  },
  computed: {
  },
  methods: {
      // 获取优惠券数量
      getCouponCount(){
          let params = {
              customer_user_id: this.customer_user_id, // 客户用户ID
          }
          getCouponNum(params).then(res => {
              if (res.code === 0) {
                  const {normal_total, used_total, invalid_total} = res.data
                  this.normal_total = normal_total || 0
                  this.used_total = used_total || 0
                  this.invalid_total = invalid_total || 0
              } 
          }).catch(err => {

          }); 
      },
     
      changeTab (e){
      }
  }
};
</script>

<style lang="scss" scoped>
.coupon-center {
  height: 100vh;
  background-color: white;
  ::v-deep .xiaoe-tabs__nav--card {
      border: none;
      height: 0.96rem;
      margin: 0;
      padding: 0 0.24rem;
      align-items: center;
  }
  ::v-deep .xiaoe-tabs--card {
      padding-top: 0;
  }
  ::v-deep .xiaoe-tabs--card .xiaoe-tabs__wrap {
      margin: 0 auto;
      width: 100%;
      max-width: 480px;
      height: 0.96rem;
  }

  ::v-deep .xiaoe-tabs--line {
      padding-top: 0;
  }

  ::v-deep .xiaoe-tabs__nav--card .xiaoe-tab {
      min-width: 1.62rem;
      height: 0.48rem;
      border-radius: 0.32rem;
      background: #f5f5f5;
      border: none;
      flex: none;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 0.24rem;
      font-size: 0.24rem;
      font-weight: 400;
      color: #333;
  }
  ::v-deep .xiaoe-tabs__nav--card .xiaoe-tab.xiaoe-tab--active {
    //   color: #1472ff;
      color: rgb(255, 120, 31);

      background-color: rgb(255, 243, 235);//#ebf3ff;
      font-weight: 500;
      font-size: 0.24rem;
  }
  ::v-deep .xiaoe-tabs__content{
      background-color: white;
  }
}
</style>
