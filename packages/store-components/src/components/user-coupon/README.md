<!--
 * @Author: joeyang <EMAIL>
 * @Date: 2025-05-13 17:57:17
 * @LastEditors: joeyang <EMAIL>
 * @LastEditTime: 2025-05-13 18:12:56
 * @FilePath: /h5-retail-shop-components/packages/store-components/src/components/user-coupon/README.md
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
## UserCoupon

### 功能介绍

优惠券列表，用于展示用于的优惠券列表，支持筛选未使用，已使用，已失效。


### 逻辑介绍

对比原来的优惠券列表，修改了这几个接口

/xe.marketing.drp.personal_code.coupon_list/1.0.0
/xe.marketing.drp.personal_code.coupon_count_info/1.0.0
/xe.marketing.drp.personal_code.coupon_cold_standby_check/1.0.0
/xe.marketing.drp.personal_code.csg.coupon.user.list/1.0.0
/xe.marketing.drp.personal_code.csg.coupon.cold_standby.user_list.get/1.0.0

换为1.0.1版本的接口


### 使用场景

目前仅支持门店下的客户详情


### 入口、出口上报监控（可选）

### 使用示例

1、引入:

```json

import storeComponents from '@xiaoe/store-components';

 components: {
        UserCoupon:storeComponents.UserCoupon
 }

<UserCoupon />

```


### 参数

#### props


#### events


#### methods

#### slot

### 流程图（可选）
