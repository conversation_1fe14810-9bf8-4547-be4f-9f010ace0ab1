<template>
    <div class="coupon-info">
        <div class="noticeBar" v-if="!loading && hasColdData1 && currentTab === 2">
            超过90天的已使用优惠券不再直接展示
            <span @click="showColdCoupon()">查看</span>
        </div>
        <div class="noticeBar" v-if="!loading && hasColdData2 && currentTab === 0">
            超过90天的已无效优惠券不再直接展示
            <span @click="showColdCoupon()">查看</span>
        </div>
        <xiaoe-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="getAllCouponList">
            <giftCoupon :item="item" v-for="item in allGiftCouponLists" :disabled="currentTab === 1 ? false : true">
                <div class="coupon-state" slot="couponState" v-if="currentTab === 2">
                    <div style="background-color:#ccc;" class="coupon-state--btn t3">已使用</div>
                    <p class="coupon-status_received"></p>
                </div>
                <div class="coupon-state" slot="couponState" v-if="currentTab === 0">
                    <div style="background-color:#ccc;" class="coupon-state--btn t3">已失效</div>
                </div>
            </giftCoupon>
            <couponItem v-for="item in couponList" :item="item" :disabled="currentTab === 1 ? false : true">
                <div class="coupon-state" slot="couponState" v-if="currentTab === 2">
                    <div style="background-color:#ccc;" class="coupon-state--btn t3">已使用</div>
                </div>
                <div class="coupon-state" slot="couponState" v-if="currentTab === 0">
                    <div v-if="item.is_use === 3" style="background-color:#ccc;" class="coupon-state--btn t3">已失效</div>
                    <div v-else style="background-color:#ccc;" class="coupon-state--btn t3">已过期</div>
                </div>
            </couponItem>
        </xiaoe-list>


        <!-- 冷备数据弹窗 -->
        <xiaoe-action-sheet v-model="coldCouponSheet" click-overlay="showColdCoupon" :title="coldActionSheetTitle">
            <div class="coldContent" @scroll="handleScroll()">
                <!-- 冷备数据列表 -->
                <xiaoe-list class="coupon-list" style="padding: 0px;" v-model="coldLoading" :finished="coldFinished"
                    finished-text="没有更多了~" @load="coldOnLoad" offset="10">
                    <div>
                        <div class="coupon-state-wrap" v-for="(item, index) in allColdCouponLists" :key="index">
                            <couponItem v-if="item.is_gift_coupon == 0" :item="item" :disabled="true">
                                <!-- 已使用 -->
                                <div class="coupon-state" slot="couponState" v-if="currentTab === 2">
                                    <div style="background-color:#ccc;" class="coupon-state--btn t3">已使用</div>
                                </div>
                                <!-- 已无效 -->
                                <div class="coupon-state" slot="couponState" v-if="currentTab === 0">
                                    <!-- 已作废 -->
                                    <div v-if="item.is_use === 3" style="background-color:#ccc;"
                                        class="coupon-state--btn t3">已失效</div>
                                    <div v-else style="background-color:#ccc;" class="coupon-state--btn t3">已过期</div>
                                </div>
                            </couponItem>
                            <coldGiftCoupon v-else-if="item.is_gift_coupon == 1" :item="item" :disabled="true">
                                <!-- 已使用 -->
                                <div class="coupon-state" slot="couponState" v-if="currentTab === 2">
                                    <div style="background-color:#ccc;" class="coupon-state--btn t3">已使用</div>
                                </div>
                                <!-- 已无效 -->
                                <div class="coupon-state" slot="couponState" v-if="currentTab === 0">
                                    <!-- 已作废 -->
                                    <div v-if="item.is_use === 3" style="background-color:#ccc;"
                                        class="coupon-state--btn t3">已失效</div>
                                    <div v-else style="background-color:#ccc;" class="coupon-state--btn t3">已过期</div>
                                </div>
                            </coldGiftCoupon>
                        </div>
                    </div>
                </xiaoe-list>
            </div>

        </xiaoe-action-sheet>
    </div>
</template>

<script>
import couponItem from './couponItem.vue'
import giftCoupon from './giftCoupon.vue'
import coldGiftCoupon from './coldGiftCoupon.vue'
import {getGiftCouponGray,getCouponListBackup,getCouponList,getIsColdStandBy,getGiftCouponDataList} from "@/apis/index"

function getQueryStrDecode(key) {
    let reg = new RegExp("(^|&)" + key + "=([^&]*)(&|$)");
    let r = window.location.search.substr(1).match(reg);
    if (r != null) {
        return decodeURI(r[2]);
    }
    return null;
}

export default {
    props: ['currentTab'],
    components: {
        couponItem,
        giftCoupon,
        coldGiftCoupon
    },
    data() {
        return {
            app_id: '',
            customer_user_id: '',
            couponList: [],
            loading: false,
            finished: false,
            pageIndex: 1,
            myCouponsTotal: 0,
            refreshing: false,
            hiddenCurrentTab: 1,
            giftCurrentIndex: 1,
            isGray: false, // 是否在灰度，true：礼品券接口，false：优惠券接口
            allGiftCouponLists: [], // 所有礼品券的数据
            hasColdData1: true, // 已使用_是否有冷备数据
            hasColdData2: true, // 已无效_是否有冷备数据
            allCouponList: [],
            giftCount: false,   //礼品券是否有多页
            coldActionSheetTitle: '',
            coldCouponSheet: false,
            coldFinished: false,
            coldLoading: true,
            allColdCouponLists: [], //所有冷备数据（礼品+优惠）
            coldCurrentIndex: 1, //冷备数据分页

        };
    },
    mounted() {
        this.refreshing = true;
        this.app_id = getQueryStrDecode('app_id')
        this.customer_user_id = getQueryStrDecode('customer_user_id')
        this.getColdState()
    },
    created() {
    },
    computed: {
    },
    methods: {

        showColdCoupon() {
            this.coldActionSheetTitle = this.currentTab === 2 ? '已使用优惠券' : '已无效优惠券'
            this.coldCouponSheet = !this.coldCouponSheet
            this.requestColdCouponList()
        },
        // 执行优惠券和礼品券接口
        getAllCouponList() {
            if (this.refreshing) {
                // 有无礼品券入口
                this.getGiftGrayFlag().then((res) => {
                    if (res) {
                        this.getGiftCouponList()
                    } else {
                        this.getCouponInfo()
                    }
                })
            } else {
                if (this.isGray && !this.giftCount) {
                    this.getGiftCouponList()
                } else {
                    this.getCouponInfo()
                }

            }
        },
        // 获取优惠券列表数据
        getCouponInfo(a) {
            let params = {
                app_id: this.app_id, // 店铺ID
                bizData: {
                    state: this.currentTab,
                    page_index: this.pageIndex
                },
                customer_user_id: this.customer_user_id // 客户用户ID
            }
            getCouponList(params).then(res => {
                if (res.code === 0) {
                    this.refreshing = false;
                    this.finished = false;
                    if (this.currentTab === 1) {
                        this.myCouponsTotal = res.data.normal_total
                    } else if (this.currentTab === 2) {
                        this.myCouponsTotal = res.data.used_total
                    } else if (this.currentTab === 0) {
                        this.myCouponsTotal = res.data.invalid_total
                    }
                    let listData = res.data.list
                    this.couponList.push(...listData)  //纯净版优惠券列表
                    this.allCouponList.push(...listData)
                    this.pageIndex++;
                    this.loading = false;
                    if (this.myCouponsTotal <= this.couponList.length) {
                        this.finished = true;
                    }
                }else {
                    this.$toast(res.msg)
                    this.loading = false;
                    this.finished = true;
                }
            }).catch(err => {
                this.loading = false;
                this.finished = true;
            });
        },
        // 冷备数据加载
        handleScroll() {
            let dom = document.querySelector('.coldContent')
            const scrollTop = dom.scrollTop
            // 变量windowHeight是可视区的高度
            const windowHeight = dom.clientHeight || dom.clientHeight
            // 变量scrollHeight是滚动条的总高度
            const scrollHeight = dom.scrollHeight || dom.scrollHeight
            if (scrollHeight - scrollTop <= windowHeight + 15) {
                if (this.allColdCouponLists.length < this.coldCouponTotal) {
                    this.requestColdCouponList()
                } else {
                    this.coldFinished = true
                    this.coldLoading = false
                }
            }
        },
        coldOnLoad() {
            // 异步更新数据
            // setTimeout 仅做示例，真实场景中一般为 ajax 请求
        },
        // 获取【冷备】优惠券数据
        requestColdCouponList() {
            let params = {
               
                use_state: this.currentTab,
                page: this.coldCurrentIndex,
                page_size: 10,
                customer_user_id: this.customer_user_id, // 客户用户ID
            };

            getCouponListBackup(params).then(res => {
                if (res.code === 0) {
                    if (res.code == 0) {
                        this.allColdCouponLists = [...this.allColdCouponLists, ...res.data.list]
                        this.coldCouponTotal = res.data.total
                        this.coldCurrentIndex++;
                        if(res.data.list.length < 10){
                            this.coldFinished = true
                        }
                    }
                }else {
                        this.coldFinished = true
                        this.coldLoading = false
                    }
            }).catch(err => {
                this.coldFinished = true
                this.coldLoading = false
            });
        },
        // 判断有无冷却数据
        getColdState() {
            let params = {
                customer_user_id: this.customer_user_id, // 客户用户ID
            }
            getIsColdStandBy(params).then(res => {
                if (this.currentTab == 0) {
                    this.hasColdData2 = res.data.has_data;
                } else if (this.currentTab == 2) {
                    this.hasColdData1 = res.data.has_data;
                }
            }).catch(err => {

            });
        },
        // 获取礼品券列表
        getGiftCouponList(item) {
            let params = {
                
                customer_user_id: this.customer_user_id, // 客户用户ID
                use_state: this.currentTab,
                page: this.giftCurrentIndex,
                page_size: 10
            }
            getGiftCouponDataList(params).then(res => {
                if (res.code === 0) {
                    const _data = res.data;
                    // 处理生成二维码，支持扫一扫
                    const datas = _data.list?.map((item) => {
                        return {
                            ...item,
                            use_code: `https://${window.APPID}.h5.xiaoeknow.com/p/t/v1/ecommerce/h5_order/writeoffTool/writeoff?type=giftCoupon&code=${item.use_code}&app_id=${window.APPID}`,
                            itemPageIndex: this.giftCurrentIndex,
                            isGift: true
                        }
                    }) || [];
                    this.allGiftCouponLists.push(...datas);  //纯净版礼品券列表
                    this.allCouponList.push(...this.allGiftCouponLists)
                    this.giftCouponsTotal = _data.total;
                    this.refreshing = false;
                    if (this.allGiftCouponLists.length === 0 || datas.length < 10) {
                        this.finished = true; // 空状态
                        this.giftCount = true;//不再加载礼品券了
                    }
                    this.getCouponInfo()
                    this.giftCurrentIndex++;
                    this.loading = false;
                }else{
                    this.$toast(res.msg)
                    this.loading = false;
                    this.finished = true; // 空状态
                }
            }).catch(err => {
                this.loading = false;
                this.finished = true; // 空状态
            });
        },
        // 是否有礼品券灰度
        getGiftGrayFlag() {
            let params = {
              
                customer_user_id: this.customer_user_id, // 客户用户ID
                gray_id: 'yee_pay' // 灰度id
            }
            return new Promise((resolve, reject) => {
                getGiftCouponGray(params).then(res => {
                    if (res.code === 0) {
                        this.isGray = res.data?.gary;
                    } else {
                        this.isGray = false;
                    }
                    resolve(this.isGray);
                }).catch(err => {
                    reject(this.isGray);
                });
            })
        }
    }
};
</script>

<style lang="scss" scoped>
.coupon-info {
    .coupon-list {
        background-color: #fff;
        height: auto;
        padding: 0 0.3rem;
        box-sizing: border-box;
        width: 100%;
        padding-bottom: 0.6rem;

        .coupon-state {
            .coupon-state--btn {
                width: 1.26rem;
                height: 0.48rem;
                border-radius: 0.28rem;
                color: #fff;
                text-align: center;
                line-height: 0.5rem;
                font-size: 0.24rem;
                background: #ff781f;
                font-weight: 500;

                p {
                    width: 1.28rem;
                    height: 1.28rem;
                    background-size: 100%;
                    margin: auto auto;
                }
            }
        }

        .dropload-container {
            background: transparent;
        }

        .coupon-status_received {
            background: url('//commonresource-1252524126.cdn.xiaoeknow.com/image/lauxmzmj0erb.png') center no-repeat;
            background-size: 100% 100%;
        }

        .coupon-status_invalid {
            background: url('//commonresource-1252524126.cdn.xiaoeknow.com/image/lauygh8d0pxi.png') center no-repeat;
            background-size: 100% 100%;
        }

        .coupon-status_expired {
            background: url('//commonresource-1252524126.cdn.xiaoeknow.com/image/lauy5vxx0fbp.png') center no-repeat;
            background-size: 100% 100%;
        }

        .clickCursor {
            cursor: pointer;
        }
    }

    .coldContent {
        padding: 5px 16px 16px;
        max-height: 300px;
        overflow: scroll;
    }

    /* 隐藏滚动条 */
    .coldContent::-webkit-scrollbar {
        width: 0;
        /* 设置滚动条宽度为0 */
    }

    /* 使滚动条轨道透明 */
    .coldContent::-webkit-scrollbar-track {
        background-color: rgba(0, 0, 0, 0);
        /* 将滚动条轨道的背景色设置为完全透明 */
    }

    // height: 100vh;
    // background-color: white;
    // ::v-deep .xiaoe-tabs__nav--card {
    //     border: none;
    //     height: 0.96rem;
    //     margin: 0;
    //     padding: 0 0.24rem;
    //     align-items: center;
    // }
    // ::v-deep .xiaoe-tabs--card {
    //     padding-top: 0;
    // }
    // ::v-deep .xiaoe-tabs--card .xiaoe-tabs__wrap {
    //     margin: 0 auto;
    //     width: 100%;
    //     max-width: 480px;
    //     height: 0.96rem;
    // }

    // ::v-deep .xiaoe-tabs--line {
    //     padding-top: 0;
    // }

    // ::v-deep .xiaoe-tabs__nav--card .xiaoe-tab {
    //     min-width: 1.62rem;
    //     height: 0.48rem;
    //     border-radius: 0.32rem;
    //     background: #f5f5f5;
    //     border: none;
    //     flex: none;
    //     display: flex;
    //     justify-content: center;
    //     align-items: center;
    //     margin-right: 0.24rem;
    //     font-size: 0.24rem;
    //     font-weight: 400;
    //     color: #333;
    // }
    // ::v-deep .xiaoe-tabs__nav--card .xiaoe-tab.xiaoe-tab--active {
    //     color: #1472ff;
    //     background-color: #ebf3ff;
    //     font-weight: 500;
    //     font-size: 0.24rem;
    // }
    // ::v-deep .xiaoe-tabs__content{
    //     background-color: white;
    // }
    .coupon-state {
        .coupon-state--btn {
            width: 1.26rem;
            height: 0.48rem;
            border-radius: 0.28rem;
            color: #fff;
            text-align: center;
            line-height: 0.5rem;
            font-size: 0.24rem;
            background: #ff781f;
            font-weight: 500;

            p {
                width: 1.28rem;
                height: 1.28rem;
                background-size: 100%;
                margin: auto auto;
            }
        }
    }

    .noticeBar {
        position: relative;
        font-size: 14px;
        // width: 100%;
        padding-left: 0.32rem;
        height: 0.8rem;
        line-height: 0.8rem;
        background-color: #FFF3EB;
        color: #FF781F;

        span {
            position: absolute;
            right: 0.32rem;
            color: #1472FF;
        }
    }
}
</style>
