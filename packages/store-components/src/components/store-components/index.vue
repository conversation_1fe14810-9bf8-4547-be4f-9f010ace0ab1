<template>
  <div>
    <div class="store-header" :class="{ fixed: isFixed }" ref="headerRef">
      <div class="left" @click="goBack"><img class="left-icon" src="https://commonresource-1252524126.cdn.xiaoeknow.com/image/maoqea4w0vvh.png" /> {{retailIndustrySolutionType.name}}管理</div>
      <div class="right" v-if="roleType == 2 || roleType == 3">
        当前{{retailIndustrySolutionType.name}}：
        <div class="store-name">{{ store && store.team_name }}</div>
        <span class="switch" @click="checkStoreList">切换</span>
      </div>
    </div>
    <BottomPopup :showPopup="showPopup" :retailIndustrySolutionType='retailIndustrySolutionType' :storeRadio="store" @selectStore="selectStore" @closePopup="closePopup" />
  </div>
</template>

<script>
import { defineComponent, onMounted, ref, watch, nextTick, computed, onBeforeUnmount, isVue2 } from 'vue-demi'
import BottomPopup from './BottomPopup.vue'
import '@xiaoe/nvwa/lib/c-community-packaging';
import Http from '@/utils/request'

export default defineComponent({
  components: {
    BottomPopup
  },
  model: {
    prop: 'modelValue',
    event: 'update:modelValue'
  },
  emits: ['getSelectTeam', 'loaded'],
  props: {
    backUrl: {
      type: String,
      default: '/'
    },
    leftJumpAddress: {
      type: String,
      default: ''
    }
  },
  setup(props, { emit }) {
    const headerRef = (ref < HTMLElement) | (null > null)
    const isFixed = ref(false)
    const showPopup = ref(false)
    const roleType = ref(-1) //-1 普通 0-店长 1-店员 2-门店负责人 3-区域经理 4-没有门店的门店员工 5-推广员 6-团队运营者
    const store = ref({})
    const retailIndustrySolutionType = ref({})
    const goBack = () => {
      if (props.leftJumpAddress) {
        window.location.href = props.leftJumpAddress
      } else {
        const targetPath = '/p/t/v1/store/main/manage/store_manage';
        if (window.location.pathname.includes('/p/t/v1/store/main/manage')) {
          // 当前已在目标模块内，无需刷新，改变路径即可
          window.history.pushState(null, '', targetPath);
        } else {
          // 当前不在目标模块，刷新页面跳转
          window.location.href = window.location.origin + targetPath;
        }
      }
    }

    const onScroll = () => {
      if (headerRef.value) {
        isFixed.value = headerRef.value.getBoundingClientRect().top <= 0
      }
    }

    const closePopup = () => {
      showPopup.value = false
    }

    const checkStoreList = () => {
      showPopup.value = true
    }

    const selectStore = (item)=>{
      // 选择门店回掉后的数据
      store.value = item;
      emit('getSelectTeam', item);
      sessionStorage.setItem('selectTeam', JSON.stringify(store.value))
    }
    // 获取第一个门店数据
    const getStoreListData = () => {
      const params = {
        page_size: 1,
        page_index: 1,
        team_id: ''
      }
      Http.request({
        url: '/xe.marketing.drp.user_manage_team_list/2.0.0',
        method: 'post',
        data: params,
        timeout: 20000
      })
        .then(({ code, data }) => {
          if (code === 0) {
            const { total, list } = data
            if (total > 0) {
              store.value = list[0];
              sessionStorage.setItem('selectTeam', JSON.stringify(store.value))
              emit('loaded',  store.value )
            }
          }
        })
        .catch(error => {})
    }

    // 获取当前用户角色
    const getCurrentRoleType = () => {
      Http.request({
        url: '/xe.marketing.drp.get_user_store_role_type/1.0.0',
        method: 'post',
        data: {},
        timeout: 20000
      })
        .then(({ code, data }) => {
          if (code === 0) {
            roleType.value = data.user_role_type
            sessionStorage.setItem('storeRoleType', roleType.value)
            if (![2, 3].includes(roleType.value)) {
              // 这里是你要执行的逻辑
              emit('loaded',  {} )
            }else{
              storeData()
            }
          }
        })
        .catch(error => {})
    }

    onMounted(async() => {
      retailIndustryType.get("retailIndustry").then((value) => {
        retailIndustrySolutionType.value = value;
        console.log('retailIndustryType.get("retailIndustry").then((value) => {});', value)
      });
      // 获取当前角色类型
      if (sessionStorage.getItem('storeRoleType') !== null) {
        roleType.value =  Number(sessionStorage.getItem('storeRoleType'))
        if (![2, 3].includes(roleType.value)) {
          // 这里是你要执行的逻辑
          emit('loaded',  {} )
        }else{
          storeData()
        }
      } else {
        getCurrentRoleType()
      }
    })
    const storeData = async()=>{
      store.value = JSON.parse(sessionStorage.getItem('selectTeam'))
      console.log('store', store.value !== null)
      if (store.value !== null) {
        emit('loaded',  store.value )
      } else {
       await getStoreListData()
      }
    }
    onBeforeUnmount(() => {
    })

    return {
      isFixed,
      headerRef,
      roleType,
      goBack,
      emit,
      store,
      checkStoreList,
      showPopup,
      closePopup,
      selectStore,
      retailIndustrySolutionType
    }
  }
})
</script>
<style>
body {
  margin: 0 !important;
}
</style>
<style scoped lang="scss">
.store-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.24rem 0.32rem;
  background-color: #fff;
  font-size: 0.28rem;
  color: #333;
  box-shadow: 0 0.02rem 0.08rem rgba(0, 0, 0, 0.05);
  z-index: 999;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

  &.fixed {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
  }

  .left {
    cursor: pointer;
    color: #333;
    display: flex;
    align-items: center;
    .left-icon {
      width: 0.28rem;
      height: 0.28rem;
    }
  }

  .right {
    display: flex;
    align-items: center;
    .store-name {
      margin: 0 0.08rem;
      max-width: 2.52rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .switch {
      color: #1677ff;
      cursor: pointer;
    }
  }
}
</style>
