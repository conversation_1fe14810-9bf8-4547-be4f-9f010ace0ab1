<template>
  <transition name="slide-up">
    <div v-if="visible" class="popup-mask" @click.self="close">
      <div class="popup-content" ref="popupRef">
        <div class="popup-header">
          <span>选择{{retailIndustrySolutionType.name}}</span>
          <button class="close-btn" @click="close">×</button>
        </div>

        <div class="popup-body" @scroll.passive="onScroll">
          <div
            class="popup-item"
            v-for="item in storeList"
            :key="item.team_id"
            :class="{ active: currentSelectId == item.team_id }"
            @click="selectItem(item)"
          >
            {{ item.team_name }}
          </div>

          <div class="loading" v-if="loading">加载中...</div>
          <div class="no-more" v-if="!hasMore">没有更多了</div>
        </div>
        <div class="footer"><div class="confirm-btn" @click="confirm">确认</div></div>
      </div>
    </div>
  </transition>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref, watch, nextTick, computed, onBeforeUnmount } from 'vue-demi'
import Http from '@/utils/request'


export default defineComponent({
  name: 'BottomPopup',
  props: {
    showPopup: Boolean,
    storeRadio: { type: Object, default: {} },
    retailIndustrySolutionType:  {type: Object, default: {}}
  },
  emits: ['closePopup', 'selectStore'],
  setup(props, { emit }) {
    const visible = ref(props.showPopup)
    const storeList = ref([
    ])
    const page = ref(1)
    const pageSize = 10
    const loading = ref(false)
    const hasMore = ref(true)
    const selectedItem = ref(props.storeRadio)
    const popupRef = ref<HTMLElement | null>(null)
    const currentSelectId = ref(props.storeRadio?.team_id)
    const fetchData = async  ()=> {
      if (loading.value || !hasMore.value) return
      loading.value = true

      const params = {
        page_size: pageSize,
        page_index: page.value,
        team_id: ''
      }
      Http.request({
        url: '/xe.marketing.drp.user_manage_team_list/2.0.0',
        method: 'post',
        data: params,
        timeout: 20000
      })
        .then(({ code, data }) => {
          if (code === 0) {
            const { total, list } = data
              // 加载状态结束
              loading.value = false
              if (data !== null) {
                storeList.value = storeList.value.concat(list)
                if (storeList.value.length >= total) {
                  hasMore.value = false
                } else {
                  page.value += 1
                }
              }
          }
        })
        .catch(error => {})
    }

    const onScroll = () => {
      const el = popupRef.value
      if (el && el.scrollTop + el.clientHeight >= el.scrollHeight - 10) {
        fetchData()
      }
    }


    onMounted(() => {
    })

    const close = () => {
      visible.value = false
      emit('closePopup')
    }

    const selectItem = item => {
      console.log('item', item)
      selectedItem.value = item
      currentSelectId.value = item.team_id
      console.log('selectedItem', currentSelectId.value)
    }

    // 选择后的数据
    const confirm = () => {
      emit('selectStore', selectedItem.value)
      close()
    }

    watch(
      () => props.showPopup,
      val => {
        visible.value = val
        if (val && storeList.value.length === 0) {
          fetchData()
        }
      }
    )
    watch(
      () => props.storeRadio,
      val => {
        if (val) {
          selectedItem.value = val;
          currentSelectId.value = val.team_id;
        }

      }
    )
    return {
      visible,
      storeList,
      selectedItem,
      loading,
      hasMore,
      popupRef,
      currentSelectId,
      close,
      onScroll,
      selectItem,
      confirm
    }
  }
})
</script>

<style scoped lang="scss">
.popup-mask {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  font-size: 0.28rem;
}

.popup-content {
  width: 100%;
  max-height: 70%;
  background: #fff;
  border-radius: 0.24rem 0.24rem 0 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .footer {
    width: 6.86rem;
    margin: auto;
    text-align: center;
    margin-bottom: 0.16rem;
    .confirm-btn {
      height: 0.76rem;
      background: #1472ff;
      color: white;
      border-radius: 0.36rem;
      line-height: 0.76rem;
    }
  }
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.24rem 0.32rem;
  border-bottom: 0.02rem solid #eee;
  font-size: 0.32rem;
  font-weight: bold;

  .close-btn {
    background: none;
    border: none;
    font-size: 0.4rem;
    cursor: pointer;
  }
}

.popup-body {
  overflow-y: auto;
  flex: 1;
  padding: 0.2rem 0.32rem;

  .popup-item {
    padding: 0.2rem 0;
    border-bottom: 0.02rem solid #f1f1f1;
    cursor: pointer;
    font-size: 0.28rem;
    transition: background 0.2s;
  }

  .popup-item.active {
    // background-color: #f0f9ff;
    color: #409eff;
    font-weight: 500;
  }

  .loading,
  .no-more {
    text-align: center;
    padding: 0.24rem;
    font-size: 0.28rem;
    color: #888;
  }
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from,
.slide-up-leave-to {
  transform: translateY(100%);
}
</style>
