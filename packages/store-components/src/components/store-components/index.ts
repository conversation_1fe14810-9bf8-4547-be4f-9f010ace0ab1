import { permissionMap } from '@/utils/permission';
import Http from '@/utils/request';
import StoreHeader from './index.vue';
/**
 * 检查当前用户是否拥有某模块的权限，如果没有权限则跳转到权限提示页
 * @param module 权限模块名（从 permissionMap 中映射 permission_id）
 * @returns Promise<boolean> - 有权限则 resolve(true)，无权限或失败则 reject(false)
 */
const permissionJump = (module: string, state:boolean): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    const params = {
      permission_id: permissionMap[module]
    };

    Http.request({
      url: '/xe.marketing.drp.store_check_user_has_node_right/1.0.0',
      method: 'post',
      data: params,
      timeout: 20000
    })
      .then(({ code, data }: { code: number; data: any }) => {
        if (code === 0) {
          resolve(true);
        } else if (code === 403) {
          // 没有权限，跳转到空白页
          if(!state){
            window.location.href = `/p/t/v1/retail/retail_shop/approval/permissionBack`;
          }
          return reject(false);
        } else {
          return reject(false);
        }
      })
      .catch((error: any) => {
        if(error?.data?.code === 403){
          // 没有权限，跳转到空白页
          if(!state){
            window.location.href = `/p/t/v1/retail/retail_shop/approval/permissionBack`;
          }
        }
        console.error(error);
        return reject(false);
      });
  });
};

export { permissionJump, StoreHeader };
