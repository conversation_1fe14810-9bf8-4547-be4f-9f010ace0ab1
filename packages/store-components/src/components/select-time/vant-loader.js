/*
 * @Author: bate bat<PERSON><PERSON>@xiaoe-tech.com
 * @Date: 2025-07-15 18:51:06
 * @LastEditors: bate <EMAIL>
 * @LastEditTime: 2025-07-16 09:49:42
 * @FilePath: /h5-retail-shop-components/packages/store-components/src/components/select-time/vant-loader.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { isVue2 } from 'vue-demi';

console.log('isVue2', isVue2)
export const loadedComponents = async ()=>{
    if (isVue2) {
        DatetimePicker = (await import('vant/es/datetime-picker')).default;
        toastFn = (await import('vant/es/toast')).default;
    }else{
        DatetimePicker = (await import('vant/es/date-picker')).default;
        toastFn = (await import('vant')).showToast;
    }
}
