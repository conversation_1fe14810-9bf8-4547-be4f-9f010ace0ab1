<template>
  <div class="customTimePanel">
    <van-action-sheet v-model:show="isShowDialog" @close="closeDialog">
      <div class="top-bar">
        <div class="left" @click="closeDialog">取消</div>
        <div class="title">自定义时间</div>
        <div class="confirm" @click="confirmFun">确认</div>
      </div>
      <div class="content">
        <div class="center-wrapper">
          <div
            class="time-style"
            :class="{ actived: timeType === 0 }"
            @click="chooseTimeFun(0)"
          >
            {{ startTimeText }}
          </div>
          <div class="split-text">至</div>
          <div
            class="time-style"
            :class="{ actived: timeType === 1 }"
            @click="chooseTimeFun(1)"
          >
            {{ endTimeText }}
          </div>
        </div>
      </div>
      <!-- Vue2: 原生 datetime-picker -->
      <van-datetime-picker
        v-if="isVue2 && timeType === 0"
        v-model="startTime"
        type="date"
        :show-toolbar="false"
      />
      <van-datetime-picker
        v-if="isVue2 && timeType === 1"
        v-model="endTime"
        type="date"
        :show-toolbar="false"
      />
      <!-- Vue3: 自定义 picker 实现 -->
      <van-date-picker
        v-if="!isVue2 && timeType === 0"
        v-model="startTimeVue3"
        title="选择日期"
        :show-toolbar="false"
      />
      <van-date-picker
        v-if="!isVue2 && timeType === 1"
        v-model="endTimeVue3"
        title="选择日期"
        :show-toolbar="false"N
      />
    </van-action-sheet>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch, computed, onMounted, isVue2, Vue, createApp } from 'vue-demi';
import { ActionSheet,Popup, Overlay, Picker } from 'vant';
let DatetimePicker: any = null;
let toastFn: any = null; 
console.log('isVue2', isVue2)
if (isVue2) {
  DatetimePicker = (await import('vant/es/datetime-picker')).default;
  toastFn = (await import('vant/es/toast')).default;
}else{
  // let datePickerJs = 'vant/es/date-picker'
  DatetimePicker = (await import('vant/es/date-picker')).default;
  toastFn = (await import('vant')).showToast;
}

export default defineComponent({
  name: 'CustomTimePanel',
  components: {
    [ActionSheet.name]: ActionSheet,
    [DatetimePicker.name]: DatetimePicker,
    [Popup.name]: Popup,
    [Overlay.name]: Overlay,
    [Picker.name]: Picker
  },
  props: {
    showCustomTime: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close', 'confirm'],
  setup(props, { emit }) {
    const isShowDialog = ref(props.showCustomTime);
    const timeType = ref(0);
    const formatDate = (date: Date | null, type) => {
      if (!date) return '';
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return type==='arr'?[year, month, day]:`${year}-${month}-${day}`;
    };
    
    const startTime = ref<Date | null>(new Date());
    const endTime = ref<Date | null>(new Date());

    const startTimeVue3 = ref(formatDate(new Date(), 'arr'));
    const endTimeVue3 = ref(formatDate(new Date(), 'arr'));

    // 自定义时间的开始时间
    const startTimeText = computed(() =>{
      if(isVue2){
        return startTime.value ? formatDate(startTime.value, isVue2?'':'arr') : '开始时间'
      }else{
        return startTimeVue3.value ? startTimeVue3.value.join('-') : '开始时间'
      }
    });
    // 自定义时间的结束时间
    const endTimeText = computed(() =>{
        if(isVue2){
          return endTime.value ? formatDate(endTime.value, isVue2?'':'arr') : '结束时间'
        }else{
          return endTimeVue3.value ? endTimeVue3.value.join('-') : '结束时间'
        }
    });

    const closeDialog = () => {
      emit('close');
    };

    const chooseTimeFun = (value: number) => {
      timeType.value = value;
      if (!startTime.value) {
        startTime.value = endTime.value;
      }
      if (!endTime.value) {
        endTime.value = startTime.value;
      }
    };

    const confirmFun = async () => {
      if (startTimeText.value === '开始时间') {
        toastFn('请选择开始时间');
        return;
      }
      if (endTimeText.value === '结束时间') {
        toastFn('请选择结束时间');
        return;
      }
      if (startTimeText.value > endTimeText.value) {
        toastFn('开始时间不能大于结束时间');
        return;
      }
      emit('confirm', [startTimeText.value, endTimeText.value]);
    };


    watch(
      () => props.showCustomTime,
      val => {
        isShowDialog.value = val;
      }
    );
    onMounted(async() => {
        if (isVue2) {
            // DatetimePicker = (await import('vant/es/datetime-picker')).default;
            // toastFn = (await import('vant/es/toast')).default;
            // const DatetimePickerStyle =
            // const popupStyle = 'vant/es/popup/index.css'
            // const overlayStyle = 'vant/es/overlay/index.css'
            // const actionSheetStyle = 'vant/es/action-sheet/index.css'
            // const toastStyle = 

            await import('vant/es/toast/index.css')
            await import('vant/es/datetime-picker/style/index.js')
            await import('vant/es/popup/index.css')
            await import('vant/es/overlay/index.css')
            await import('vant/es/action-sheet/index.css')

            // let toastStyle = 'vant/es/toast/index.css'
            // let datetimePickerStyle = 'vant/es/datetime-picker/style/index.js'
            // let popupStyle = 'vant/es/popup/index.css'
            // let overlayStyle = 'vant/es/overlay/index.css'
            // let actionSheetStyle = 'vant/es/action-sheet/index.css'
            // await import(toastStyle)
            // await import(datetimePickerStyle)
            // await import(popupStyle)
            // await import(overlayStyle)
            // await import(actionSheetStyle)
        } else {
            // const datePickerData = 'vant/es/date-picker'
            // DatetimePicker = (await import(datePickerData)).default;
            // toastFn = (await import('vant')).showToast;
            // const pickerStyle = 'vant/es/picker/style/index.mjs'

            // let toastStyle = 'vant/es/toast/style/index.mjs'
            // let actionSheetStyle = 'vant/es/action-sheet/style/index.mjs'
            // let overlayStyle = 'vant/es/overlay/style/index.mjs'
            // let popupStyle = 'vant/es/popup/style/index.mjs'
            // let pickerStyle = 'vant/es/picker/style/index.mjs'
            // await import(overlayStyle)
            // await import(toastStyle)
            // await import(actionSheetStyle)
            // await import(pickerStyle)
            // await import(popupStyle)

            await import('vant/es/popup/style/index.mjs')
            await import('vant/es/overlay/style/index.mjs')
            await import('vant/es/toast/style/index.mjs')
            await import('vant/es/action-sheet/style/index.mjs')
            await import('vant/es/picker/style/index.mjs')

        }
    })

    return {
      isShowDialog,
      timeType,
      startTime,
      endTime,
      startTimeText,
      endTimeText,
      closeDialog,
      chooseTimeFun,
      confirmFun,
      isVue2,
      startTimeVue3,
      endTimeVue3
    };
  }
});
</script>

<style lang="scss" scoped>
.customTimePanel {
  .top-bar {
    display: flex;
    height: 48px;
    justify-content: space-between;
    padding: 0 16px;
    line-height: 48px;
    border-bottom: 1px solid #ebebeb;
    .title {
      font-size: 16px;
      font-weight: 600;
    }
    .left {
      color: #999;
      font-size: 0.28rem;
    }
    .confirm {
      color: #1472ff;
      font-size: 0.28rem;
    }
  }
  .content {
    padding: 16px;
    .center-wrapper {
      display: flex;
      justify-content: start;
      align-items: center;
      color: #999;
      font-size: 0.28rem;
      font-weight: 500;
      .time-style {
        line-height: 32px;
        height: 32px;
        border-radius: 10px;
        padding: 0 20px;
        width: 136px;
        text-align: center;
        position: relative;
      }
      .actived {
        &::before {
          content: '';
          position: absolute;
          height: 3px;
          width: 30px;
          border-radius: 4px;
          background: #1472ff;
          bottom: 0;
          left: 64px;
        }
      }
      .split-text {
        color: #999;
      }
    }
    .confirm-btn {
      background-color: #1472ff;
      color: #fff;
      text-align: center;
      width: 100%;
      height: 38px;
      line-height: 38px;
      margin: 32px 0;
      border-radius: 10px;
    }
  }
}
</style>
