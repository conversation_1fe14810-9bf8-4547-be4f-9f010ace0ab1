/*
 * @Author: bate bat<PERSON><PERSON>@xiaoe-tech.com
 * @Date: 2025-07-11 17:51:56
 * @LastEditors: bate <EMAIL>
 * @LastEditTime: 2025-07-11 17:52:18
 * @FilePath: /h5-retail-shop-components/packages/store-components/src/components/select-time/useVantComponents.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// useVantComponents.ts
import { isVue2 } from 'vue-demi'

// 全部静态导入（无论 Vue2 或 Vue3 都会编译）
import {
    ActionSheet as VantActionSheet,
    DatetimePicker as VantDatetimePicker,
    Overlay as VantOverlay,
    Picker as VantPicker,
    Popup as VantPopup,
    Toast as VantToast
} from 'vant'

// Vue 3 中 DatetimePicker 是 undefined，Vue 2 中 Picker 存在
const Toast = VantToast
const ActionSheet = VantActionSheet
const Popup = VantPopup
const Overlay = VantOverlay
const Picker = VantPicker
const DatetimePicker = isVue2 ? VantDatetimePicker : null

export {
    ActionSheet,
    DatetimePicker, Overlay,
    Picker, Popup, Toast
}

