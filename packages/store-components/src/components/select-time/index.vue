<template>
    <div class="date-select">
        <div class="date-pick">
            <van-popover v-model:show="showDateType" trigger="click">
                <div class="date-type-selecter" v-show="showDateType">
                    <div :class="['type-item', { 'active-item': item.day === dateType }]" v-for="item in dateTypeList" :key="item.text" @click="selectType(item.day)">{{ item.text }}</div>
                </div>
                <template #reference>
                    <div class="date-type">
                        {{ dataTypeText }}
                        <img :class="['arrow-icon', { rotated: showDateType }]" src="./images/down_arrow.png" />
                    </div>
                </template>
            </van-popover>
        </div>
        <custom-time-panel
            :showCustomTime="showCalendar"
            @close="closeCustomTime"
            @confirm="onConfirm">
        </custom-time-panel> 
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch, isVue2, onMounted } from 'vue-demi';
import dayjs from 'dayjs';
import customTimePanel from './customTimePanel.vue';
import { Popover } from 'vant';

const TIME_MAP = {
  ALL_TIME: 0,
  TODAY: 1,
  YESTERDAY: -2,
  WEEK: 7,
  MONTH: 30,
  QUARTER: 90,
  HALF_YEAR: 6,
  CUSTOM_TIME: -1
};
console.log('isVue2', isVue2)
export default defineComponent({
  name: 'DateSelect',
  components: {
    customTimePanel,
    [Popover.name]: Popover
  },
  props: {
    defaultDateType: {
      type: Number,
      default: TIME_MAP.ALL_TIME
    }
  },
  emits: ['getTimeData'],
  setup(props, { emit }) {
    const showDateType = ref(false);
    const showCalendar = ref(false);
    const dateType = ref(props.defaultDateType);
    const startDate = ref('');
    const endDate = ref('');

    const dateTypeList = [
      { text: '全部时间', day: TIME_MAP.ALL_TIME },
      { text: '今天', day: TIME_MAP.TODAY },
      { text: '昨日', day: TIME_MAP.YESTERDAY },
      { text: '近7天', day: TIME_MAP.WEEK },
      { text: '近30天', day: TIME_MAP.MONTH },
      { text: '近90天', day: TIME_MAP.QUARTER },
      { text: '近半年', day: TIME_MAP.HALF_YEAR },
      { text: '自定义', day: TIME_MAP.CUSTOM_TIME }
    ];

    const dataTypeText = computed(() => {
      return dateTypeList.find(item => item.day === dateType.value)?.text || '';
    });

    const dateRangeText = computed(() => {
      if (startDate.value && endDate.value) {
        return `${startDate.value.replace(/-/g, '/')} - ${endDate.value.replace(/-/g, '/')}`;
      }
      return '请选择日期';
    });

    const selectType = (type: number) => {
      dateType.value = type;
      showDateType.value = false;
      if (type === TIME_MAP.CUSTOM_TIME) {
        showCalendar.value = true;
      }
    };

    const closeCustomTime = () => {
      showCalendar.value = false;
    };

    const onConfirm = (date: [string, string]) => {
      startDate.value = date[0];
      endDate.value = date[1];
      showCalendar.value = false;
      emit('getTimeData', {
        startDate: startDate.value,
        endDate: endDate.value,
        dateType: dateType.value
      });
    };

    const getSelectDate = (val: number) => {
      const today = dayjs().format('YYYY-MM-DD');
      const yesterday = dayjs().subtract(1, 'day').format('YYYY-MM-DD');

      const hasTime = [
        TIME_MAP.WEEK,
        TIME_MAP.MONTH,
        TIME_MAP.HALF_YEAR,
        TIME_MAP.TODAY,
        TIME_MAP.YESTERDAY,
        TIME_MAP.QUARTER
      ].includes(val);

      if (hasTime) {
        switch (val) {
          case TIME_MAP.TODAY:
            startDate.value = today;
            endDate.value = today;
            break;
          case TIME_MAP.YESTERDAY:
            startDate.value = yesterday;
            endDate.value = yesterday;
            break;
          default:
            startDate.value = dayjs()
              .subtract(val, val === TIME_MAP.HALF_YEAR ? 'month' : 'day')
              .format('YYYY-MM-DD');
            endDate.value = today;
            break;
        }
      } else {
        startDate.value = '';
        endDate.value = '';
      }

      emit('getTimeData', {
        startDate: startDate.value,
        endDate: endDate.value,
        dateType: dateType.value
      });
    };

    onMounted(async() => {
      console.log('isVue2', isVue2)
        if (isVue2) {
            // await import('vant/es/popover/index.css')
            let popoverStyle = new URL(`vant/es/popover/index.css`, import.meta.url).href
            await import(popoverStyle)
        } else {
            let popoverStyle = new URL(`vant/es/popover/style/index.mjs`, import.meta.url).href
            await import(popoverStyle)
        }
    })


    // 初始化 & 监听 dateType
    watch(
      () => dateType.value,
      val => {
        if (val !== TIME_MAP.CUSTOM_TIME) {
          getSelectDate(val);
        }
      },
      { immediate: true }
    );

    return {
      showDateType,
      showCalendar,
      dateType,
      startDate,
      endDate,
      dateTypeList,
      dataTypeText,
      dateRangeText,
      selectType,
      closeCustomTime,
      onConfirm
    };
  }
});
</script>

<style scoped lang="scss">
.date-select {
    .date-pick {
        font-size: 0.28rem;
        color: #666666;
        position: relative;
        .date-type {
            min-width: 1.44rem;
            text-align: right;
            .arrow-icon {
                width: 0.24rem;
                height: 0.24rem;
            }
            .rotated {
                transform: rotate(180deg);
            }
        }
    }
}

.date-type-selecter {
    font-size: 0.28rem;
    color: #666666;
    border-radius: 0.08rem;
    .type-item {
        width: auto;
        box-sizing: border-box;
        padding: 0.24rem 0.32rem;
        background-color: #ffffff;
        &:not(:last-child) {
            border-bottom: 1px solid #ebedf0;
        }
        &:first-child {
            border-top-left-radius: 0.08rem;
            border-top-right-radius: 0.08rem;
        }
        &:last-child {
            border-bottom-left-radius: 0.08rem;
            border-bottom-right-radius: 0.08rem;
        }
    }
    .active-item {
        color: #ff5429;
    }
}
</style>
