<template>
  <div class="goods-card-wrapper">
    <div class="goods-card-content" @click="goToDetail">
      <div class="user-info">
        <div class="title">
          <img :src="userImage" alt="" />
          <span class="user-name">{{ userName }}</span>
        </div>
        <div class="order-status" :class="orderStatusClass">{{ orderStatusText }}</div>
      </div>
      <div v-for="(item, index) in goodsList" :key="index" class="goods-list-wrapper">
        <div class="goods-item-wrapper">
          <div class="goods-img">
            <img :src="item.img_url" alt="goods-img" />
            <div class="goods-tag">{{ item.resource_type_text }}</div>
          </div>
          <div class="goods-info-content">
            <div class="item-row">
              <span class="weight-600 goods-name">{{ item.goods_name || '-' }}</span>
              <span class="grey-tips">x{{ item.goods_buy_num }}</span>
            </div>
            <div class="item-row center-row">
              <span class="grey-tips goods-spec">{{ item.goods_spec_description }}</span>
              <span class="status" :class="item.check_state === 1?'color-orange':'color-gray'">
                {{ getCheckAndRefundStateText(item.check_state, item.aftersale_show_state) }}
              </span>
            </div>
            <div class="item-row">
              <div class="item-row-left">
                <div class="package-tag" v-if="getTagText(item.relation_goods_type, item.relation_order_goods_id)">
                  {{ getTagText(item.relation_goods_type, item.relation_order_goods_id) }}
                </div>
              </div>
              <div></div>
            </div>
            <div class="item-row bottom-row">
              <div class="item-row-left"></div>
              <span class="weight-600">¥{{ (item.unit_price / 100).toFixed(2) }}</span>
            </div>
          </div>
        </div>
        <div class="mode-wrapper">
          <div class="title grey-tips">结账模式</div>
          <div class="right">
            <span class="grey-tips">{{ getCommissionText(item.is_commission) }}</span>
            <span class="grey-tips contact-tips">{{ getCommissionTipsText(item.is_commission) }}</span>
          </div>
        </div>
      </div>
      <div class="summary-wrapper">
        <div class="total">
          <span>实收金额：</span>
          <span class="color-red">¥{{ (actualFee / 100).toFixed(2) }}</span>
        </div>
      </div>
      <div class="order-info-wrapper">
        <div class="item">下单时间：{{ createAt }}</div>
        <div class="item" v-if="expressCheckTime">核销时间：{{ expressCheckTime }}</div>
        <div class="item order-copy">
          <div>订单号：{{ orderId }}</div>
          <img
            class="copy-btn"
            src="https://commonresource-1252524126.cdn.xiaoeknow.com/image/m9i66ac30l13.png"
            alt=""
            @click="handleCopy"
          />
        </div>
        <div class="item">所属员工：{{ employee || '--' }}</div>
        <div class="item" v-if="isInStoreTaxationGrey">
          收款方：{{ getMerchantTypeText || '--' }}
          <van-popover
            v-if="isInStoreTaxationGrey && getMerchantTypeText === '总部' && switchReason"
            v-model="showResultTips"
            placement="top"
            trigger="click"
          >
            <div class="pop-tips">
              {{ switchReason }}
            </div>
            <template #reference>
              <img
                class="img-icon"
                src="https://commonresource-1252524126.cdn.xiaoeknow.com/image/m9tttono022e.png"
                alt=""
                @click.stop="handleIconClick"
              />
            </template>
          </van-popover>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'goodsCard',
  data() {
    return {
      clipboard: null,
      showResultTips: false
    }
  },
  props: {
    // 商品列表
    goodsList: {
      type: Array,
      default: () => []
    },
    // 实际金额
    actualFee: {
      type: Number,
      default: 0
    },
    // 订单状态
    orderState: {
      type: Number,
      default: 0
    },
    // 用户昵称
    userName: {
      type: String,
      default: ''
    },
    // 用户头像
    userImage: {
      type: String,
      default: ''
    },
    // 下单时间
    createAt: {
      type: String,
      default: ''
    },
    // 核销时间
    expressCheckTime: {
      type: String,
      default: ''
    }, 
    // 订单号
    orderId: {
      type: String,
      default: ''
    },
    // 所属原因
    employee: {
      type: String,
      default: ''
    },
    // 团队id
    teamId: {
      type: String,
      default: ''
    },
    // 总部原因
    switchReason: {
      type: String,
      default: ''
    },
    // 收款方
    merchantType: {
      type: Number,
      default: ''
    },
    // 门店灰度
    isInStoreTaxationGrey: {
      type: Boolean,
      default: false
    },
    // 是否跳转到详情
    isGoToDetail: {
      type: Boolean,
      default: false
    }
  },
  beforeDestroy() {
    this.clipboard.destroy()
  },
  computed: {
    orderStatusText() {
      const statusMap = {
        0: '待付款',
        1: '待成交',
        2: '待发货',
        3: '已发货',
        4: '已完成',
        5: '已关闭'
      }
      return statusMap[this.orderState]
    },
    getMerchantTypeText() {
      const statusMap = {
        0: '--',
        1: '总部',
        2: '门店',
        3: "无需支付"
      }
      return statusMap[this.merchantType]
    },
    orderStatusClass() {
      const classMap = {
        0: 'status-red', // 待付款
        1: 'status-deal', // 待成交
        2: 'status-blue', // 待发货
        3: 'status-success', // 已发货
        4: 'status-success', // 已完成
        5: 'status-closed' // 已关闭
      }
      return classMap[this.orderState]
    }
  },
  methods: {
    handleIconClick(e) {
      e.stopPropagation()
      this.showResultTips = !this.showResultTips
    },
    handleCopy(e) {
      e.stopPropagation()
      navigator.clipboard
        .writeText(this.orderId)
        .then(() => {
          this.$toast('复制成功')
        })
        .catch(err => {
          console.error('复制失败:', err)
        })

    },
    goToDetail() {
      if (!this.isGoToDetail) {
        return
      }
      this.$router.push({
        path: '/detail',
        query: {
          order_id: this.orderId,
          team_id: this.teamId
        }
      })
    },
    getTagText(type, id) {
      if ((type === 2 || type === 1) && id) {
        return '套餐商品'
      }
      if (type === 5 && id) {
        return '组合商品'
      }
      return ''
    },
    getCommissionText(state) {
      const map = {
        0: '未线上结账',
        1: '线上自动结账',
        2: '未分类'
      }
      return map[state]
    },
    getCommissionTipsText(state) {
      const map = {
        2: '(当天订单模式确认中，以往订单为总部收款结账订单)'
      }
      return map[state]
    },
    getCheckAndRefundStateText(checkState, refundState) {
      const checkStateMap = {
        0: '无需核销',
        1: '待核销',
        2: '已核销',
        3: '已失效'
      }
      const refundStateMap = {
        0: '无售后',
        1: '售后中',
        2: '部分退款',
        3: '已全额退款',
        4: '退款中'
      }
      // 无售后不展示
      if (refundState === 0) {
        return checkStateMap[checkState]
      }
      const checkText = checkStateMap[checkState]
      const refundText = refundStateMap[refundState]
      return `${checkText}(${refundText})`
    }
  }
}
</script>

<style lang="scss" scoped>
.pop-tips {
  padding: 0.24rem;
  font-size: 0.24rem;
  line-height: 0.32rem;
}
.goods-card-wrapper {
  background: #f5f5f5;
  padding: 0 0.32rem 0.32rem;
  font-size: 0.28rem;
  color: #333;
  .goods-card-content {
    background: #fff;
    padding: 0.32rem;
    border-radius: 0.16rem;
  }
  .user-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.16rem;
    .title {
      display: flex;
      align-items: center;
      img {
        width: 0.6rem;
        height: 0.6rem;
        border-radius: 0.6rem;
        margin-right: 0.12rem;
      }
      .user-name {
        font-weight: 600;
        max-width: 3.5rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;
      }
    }
  }
  .goods-list-wrapper {
    padding: 0.2rem 0;
    .goods-item-wrapper {
      display: flex;
    }
    .goods-img {
      width: 1.8rem;
      height: 1.8rem;
      margin-right: 0.2rem;
      border: 1px solid #ebebeb;
      position: relative;
      img {
        border-radius: 0.08rem;
        width: 100%;
        height: 100%;
      }
      .goods-tag {
        position: absolute;
        left: 0.08rem;
        bottom: 0.08rem;
        height: 0.32rem;
        line-height: 0.32rem;
        display: flex;
        align-items: center;
        padding: 0 0.12rem;
        border-radius: 0.04rem;
        background: rgba(51, 51, 51, 0.5);
        font-size: 0.18rem;
        color: #fff;
      }
    }
    .goods-info-content {
      flex: 1;
      .item-row {
        display: flex;
        justify-content: space-between;
      }
      .center-row {
        margin-top: 0.08rem;
      }
      .package-tag {
        border: 1px solid #ffa51f;
        border-radius: 2px;
        padding: 0.04rem 0.08rem;
        color: #ffa51f;
        font-size: 0.24rem;
      }
      .goods-name {
        max-width: 3.6rem;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 6; /* 显示几行就写几 */
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
      .goods-spec {
        font-size: 0.24rem;
        max-width: 3.2rem;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 4; /* 限制显示4行 */
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
  .weight-600 {
    font-weight: 600;
  }
  .color-orange {
    color: #ffa51f;
    font-size: 0.24rem;
  }
  .color-gray {
    font-size: 0.24rem;
    color: #999;
  }
  .color-red {
    color: #ff4747;
    font-size: 0.32rem;
    font-weight: 600;
  }
  .grey-tips {
    color: #999;
  }
  .contact-tips {
    font-size: 0.24rem;
    margin-top: 0.16rem;
  }
  .mode-wrapper {
    margin-top: 0.16rem;
    font-size: 0.24rem;
    display: flex;
    justify-content: space-between;
    .right {
      display: flex;
      flex-direction: column;
      align-items: end;
    }
  }
  .summary-wrapper {
    font-size: 0.28rem;
    display: flex;
    justify-content: end;
    .total {
      font-weight: 600;
    }
  }
  .order-info-wrapper {
    margin-top: 0.32rem;
    border-top: 1px solid #ebebeb;
    padding-top: 0.32rem;
    .item {
      font-size: 0.24rem;
      color: #999;
      line-height: 0.4rem;
    }
    .order-copy {
      display: flex;
      justify-content: space-between;
      img {
        width: 0.32rem;
        height: 0.32rem;
      }
    }
  }
  .img-icon {
    width: 0.26rem;
    height: 0.26rem;
    margin-top: 0.1rem;
  }
}
.order-status {
  &.status-red {
    color: #fb6161;
  }
  &.status-deal {
    color: #ffa51f;
  }
  &.status-blue {
    color: #1472ff;
  }
  &.status-success {
    color: #2fce63;
  }
  &.status-closed {
    color: #ccc;
  }
}
</style>
