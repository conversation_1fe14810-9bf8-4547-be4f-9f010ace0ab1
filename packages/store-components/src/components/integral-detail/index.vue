<template>
    <div class="details-container">
        <div class="filter-bar ET5 EC15 EC10_BG fixed-compo-width">
            <xiaoe-dropdown-menu activeColor="#FF8205">
                <xiaoe-dropdown-item
                    v-model="inteType"
                    :options="typeList"
                    :titleClass="inteType ? 'focus-color' : ''"
                    @change="chooseType"
                />
                <xiaoe-dropdown-item
                    :title="inteDate"
                    :titleClass="inteDate == '当前月' ? '' : 'focus-color'"
                    @open="chooseTime"
                />
            </xiaoe-dropdown-menu>
            <xiaoe-popup v-model="showTimePicker" position="bottom">
                <xiaoe-datetime-picker
                    v-model="currentDate"
                    type="year-month"
                    :minDate="minDate"
                    :maxDate="maxDate"
                    @confirm="confirmDate"
                    @cancel="showTimePicker = false"
                />
            </xiaoe-popup>
        </div>
        <div class="detail-list">
            <div class="list-container EC10_BG">
                <xiaoe-list 
                    v-model="loading"
                    :finished="finished"
                    finished-text=""
                    @load="onLoad">
                    <div v-for="item in orderList" :key="item.flow_id" class="list" @click="toDetail(item)">
                        <div>
                            <div class="ET5 EC15 list-title">
                                {{ item.business_type_text }}
                            </div>
                            <div class="ET6 EC11">{{ item.created_at }}</div>
                        </div>
                        <div class="list-right">
                            <div class="count-wrapper">
                                <div class="ET5 inte-count" :class="item.flow_type !== 2 ? 'EC17' : 'EC15'">
                                    {{ inteFlowType(item.flow_type) }}{{ item.flow_count }}
                                </div>
                                <div v-if="isShowBalance" class="ET6 EC11">积分余额：{{ item.balance }}</div>
                            </div>
                            <i class="iconfont icon-icon_arrow2" />
                        </div>
                    </div>
                </xiaoe-list>
            </div>
            <xiaoe-empty
                v-if="dataLoaded"
                image="https://commonresource-1252524126.cdn.xiaoeknow.com/image/l8e86ttr0ll5.png	"
                description="没有积分记录"
            />
           
        </div>
    </div>
  </template>
  <script>
  import { formatDate,getQueryParam } from '@/utils/index';
  import { getPointDetail, getDisPlayIntegralBalanceGrey } from '@/apis/index'; // api
  export default {

    data() {
        return {
            loading: false,
            finished: false,
            showTimePicker: false, //显示时间选择器
            minDate: new Date(2020, 8, 1), //最小日期
            maxDate: new Date(), //最大日期
            currentDate: new Date(), //当前选择的日期
            inteDate: '当前月', //当前选择的时间，用于展示在顶部筛选栏
            dateParam: '', //传给后端的月份字段
            inteType: 0,
            typeList: [
                //  可选择的积分获取类型
                { text: '全部类型', value: 0 },
                { text: '积分获取', value: 1 },
                { text: '积分消耗', value: 2 }
            ],
            pageIndex: 1,
            pageSize: 10,
            orderList: [], //明细列表
            dataLoaded: false, //明细已加载,
            currentPage: 1,
            lfixheight: 0,
            type: 1,
            isShowBalance: true, // 是否显示积分余额
        };
    },
    created() {
        this.getGreyData();
    },
    methods: {
        onLoad() {
            this.loading = true
            this.dateParam = formatDate(this.currentDate, 'yyyy_MM');
            const checkUserId = getQueryParam('customer_user_id');
            const params = {
                select_type: this.inteType,
                operate_at: this.dateParam,
                page: this.currentPage,
                check_user_id: checkUserId ? checkUserId : ''
            };
            getPointDetail(params).then(res => {
                if (res.code === 0) {
                    const result = res.data;
                    if (result.list && result.list.length > 0) {
                        this.orderList = this.orderList.concat(result.list);
                        console.log(this.orderList, 'orderlist');
                    }
                    this.finished = (this.orderList.length >= result.page.total)
                    this.dataLoaded = this.orderList.length === 0;
                    this.currentPage++;
                } else {
                    this.dataLoaded = true;
                    this.finished = true;
                    console.log(res.msg);
                }
            }).catch(() => {
            this.dataLoaded = true;
            this.finished = true;
          })
            .finally(() => {
                this.loading = false
            });
        },
        // 获取店铺是否在隐藏积分余额的灰度名单内
        getGreyData() {
            let params = {
                gray_key: 'shield_point_detail',
            };
            getDisPlayIntegralBalanceGrey(params)
                .then(res => {
                    if(res.code === 0) {
                        this.isShowBalance = !res.data?.is_gray;
                    }
                });
        },
        //积分变化类型
        inteFlowType(type) {
            if (type == 1) {
                return '+';
            } else if (type == 2) {
                return '-';
            }
            return '冻结中';
        },
        //选择类型
        chooseType() {
            this.orderList = [];
            this.currentPage = 1;
            this.onLoad()
        },
        //  日期选择期的确认事件
        confirmDate() {
            this.orderList = [];
            this.currentPage = 1;
            this.showTimePicker = false;
            this.inteDate = formatDate(this.currentDate, 'yyyy年MM月');
            this.onLoad()
        },
        //  打开时间选择器的事件
        chooseTime() {
            this.showTimePicker = true;
            // this.isChooseTime = true
        },
        toDetail(item) {
            const query = {
                id: item.flow_id,
                operate_at: this.dateParam,
                userId: this.getQueryParam('customer_user_id') || this.getQueryParam('userId'),
            }
            window.location.href = `/p/t/v1/point/integral_h5/point/order_detail?${
                Object.keys(query)
                    .filter(key => query[key] !== null && query[key] !== '')
                    .map(key => `${key}=${query[key]}`)
                    .join('&')
            }`;
        },
        // 获取url中的参数
        getQueryParam(name) {
            const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`, 'i');
            const r = window.location.search.substring(1).match(reg);
            if (r != null) {
                return decodeURIComponent(r[2]);
            }
            return null;
        }
    }
  };
  </script>
  <style lang="scss" scoped>
  .details-container {
   
    background: #f5f5f5;
    .filter-bar {
        border-bottom: 0.02rem solid #eeeeee;
        z-index: 1;
    }
    .detail-list {
        min-height: 7.5rem;
    }
    .list-container {
        padding-left: 0.32rem;
        box-sizing: border-box;
        background-color: #FFF;
        // overflow-y: scroll;
        .list {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 0.28rem;
            padding: 0.16rem 0.32rem 0.16rem 0;
            box-sizing: border-box;
            border-bottom: 0.02rem solid #eee;
            .list-title {
                margin-bottom: 0.08rem;
            }
            .list-right {
                display: flex;
                align-items: center;
                i {
                    font-size: 0.24rem;
                    color: #999;
                }
                .count-wrapper {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-end;
                    margin-right: 0.16rem;
                    .inte-count {
                        margin-bottom: 0.14rem;
                    }
                }
            }
        }
    }
  }
  </style>
  <style lang="scss">
  .xiaoe-popup--bottom {
    border-radius: 0.2rem 0.2rem 0 0;
    overflow: hidden;
  }
  .xiaoe-dropdown-menu__bar {
    height: unset !important;
    box-shadow: unset !important;
    padding: 0.28rem 0 0.24rem 0;
  }
  .xiaoe-popup .xiaoe-picker .xiaoe-picker__toolbar .xiaoe-picker__cancel {
    color: #333333 !important;
  }
  .xiaoe-popup .xiaoe-picker .xiaoe-picker__toolbar .xiaoe-picker__confirm {
    color: #ff8205 !important;
  }
  .focus-color {
    color: #ff8205 !important;
  }
  .EC17 {
      color: #ff8205;
  }
  </style>
  