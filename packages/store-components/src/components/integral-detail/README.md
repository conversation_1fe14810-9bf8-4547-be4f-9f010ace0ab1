<!--
 * @Author: joeyang <EMAIL>
 * @Date: 2025-05-13 17:57:17
 * @LastEditors: joeyang <EMAIL>
 * @LastEditTime: 2025-05-13 18:15:47
 * @FilePath: /h5-retail-shop-components/packages/store-components/src/components/user-coupon/README.md
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
## IntegralDetail

### 功能介绍

积分详情，用于展示用户的积分列表。


### 逻辑介绍

对比原来的积分列表，修改了这个接口
/xe.point.integral.detail/1.0.0


换为
/xe.marketing.drp.point/xe.point.integral.detail/1.0.0


### 使用场景

目前仅支持门店下的客户详情


### 入口、出口上报监控（可选）

### 使用示例

1、引入:

```json

import storeComponents from '@xiaoe/store-components';

 components: {
        IntegralDetail:storeComponents.IntegralDetail
 }

<IntegralDetail />

```


### 参数

#### props


#### events


#### methods

#### slot

### 流程图（可选）
