/*
 * @Author: bate bat<PERSON><PERSON>@xiaoe-tech.com
 * @Date: 2025-05-06 11:34:08
 * @LastEditors: bate <EMAIL>
 * @LastEditTime: 2025-05-06 11:34:12
 * @FilePath: /h5-retail-shop-components/packages/store-components/src/utils/permission.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export const permissionMap = {
    AlivePromoterNodeId: 1, // 直播推广
    GoodsPromoterNodeId: 2, // 商品推广
    InviteCustomerNodeId: 3, // 邀请客户
    WriteOffNodeId: 4, // 核销
    GoodsStatNodeId: 5, // 商品统计
    OrderRecordNodeId: 6, // 订单记录
    ViewStoreAllOrderNodeId: 7, // 查看全店订单记录
    AfterSalesNodeId: 8, // 售后
    AchievementAccountNodeId: 9, // 业绩账户
    StoreAccountNodeId: 10, // 门店账户
    StoreJinXiaoCunNodeId: 11, // 门店进销存
    DistributePerformanceNodeId: 12, // 推广业绩（旧）
    StoreManageNodeId: 13, // 门店管理
    InviteStoreManagerNodeId: 14, // 邀请门店负责人
    SetPickupNodeId: 15, // 自提点设置
    InviteStaffNodeId: 16, // 邀请员
    InviteStoreCaptainNodeId: 17, // 邀请店长
    StaffManageNodeId: 18, // 员工管理
    AuditStaffApplyNodeId: 19, // 审核员工申请
    DownloadCenterNodeId: 20, // 下载中心
    StoreCustomerNodeId: 21, // 客户管理
    ViewStoreAllCustomerNodeId: 22, // 查看全店客户
    AssignCustomerNodeId: 23, // 分配客户
    AssignPointNodeId: 24, // 发放积分
    AdjustMembersNodeId: 25, // 调整会员
    ViewCustomerPhoneNodeId: 26, // 查看客户手机号
    LevelMembershipNodeId: 27, // 等级会员
    RedPocketSetNodeId: 28, // 红包设置
    CommunitySolitaireNodeId: 29, // 社区接龙
    AliveDataNodeId: 30, // 直播数据
    ViewStoreAllAliveDataNodeId: 31, // 查看全店直播数据
    DataStatisticsNodeId: 32, // 数据统计
    GiftCouponStatNodeId: 33, // 礼品券统计
    CouponStatNodeId: 34 // 优惠券统计
};
