import axios from 'axios'

// 创建axios实例
const service = axios.create({
  timeout: 10000 // 请求超时时间
})

// request拦截器
service.interceptors.request.use(
  config => {
    return config
  },
  err => {
    return Promise.reject(err)
  }
)

// response拦截器
service.interceptors.response.use(
  res => {
    if (res.data && res.data.code !== 0) {
      return Promise.reject(res)
    }
    return Promise.resolve(res.data)
  },
  err => {
    return Promise.reject(err)
  }
)

export default service
