/*
 * @Author: joeyang <EMAIL>
 * @Date: 2025-05-07 10:22:21
 * @LastEditors: joeyang <EMAIL>
 * @LastEditTime: 2025-05-07 14:33:31
 * @FilePath: /h5-retail-shop-components/packages/store-components/src/apis/request.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import {request} from '@xiaoe/js-tools/lib/request';

const getTeamIdParam = () => {
    if (sessionStorage.getItem(`${window.USERID}_team_id`)) {
        return {
            team_id: sessionStorage.getItem(`${window.USERID}_team_id`),
        };
    } else {
        return {};
    }
};

// 封装request方法，统一添加TeamId参数
const requestWithTeamId = (requestConfig) => {
    const teamIdParam = getTeamIdParam();
    // 合并参数
    if (requestConfig.params) {
        requestConfig.params = {
            ...requestConfig.params,
            ...teamIdParam
        };
    } else {
        requestConfig.params = teamIdParam;
    }

    return request(requestConfig);
};
export default requestWithTeamId;