/*
 * @Author: joeyang <EMAIL>
 * @Date: 2025-04-17 10:05:44
 * @LastEditors: joeyang <EMAIL>
 * @LastEditTime: 2025-05-06 18:14:50
 * @FilePath: /h5_store_customer_fe/src/api/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from './request';

export const getBindMembersList = (params) => {
    return request({
        method: 'post',
        url: '/xe.marketing.drp.distribute_team_customers/1.0.0',
        params,
        pack : '',
        requestType: 'json'  // 新增参数
    });
};

// 门店管理-获取店员列表
export const getMembersList = (params) => {
    return request({
        method: 'post',
        url: '/xe.marketing.drp.distribute_team_numbers/1.0.0',
        params,
        pack : '',
        requestType: 'json'  // 新增参数
    });
};

// 获取门店各个入口权限
export const getStoreEntryPermission = () => {
    return request({
        method: 'post',
        url: '/xe.marketing.drp.distribute_team_rights_get/1.0.0',
        params: {},
        pack : '',
        requestType: 'json'  // 新增参数
    });
};

// 批量换绑客户 
export const batchChangeCustomerMember = (params) => {
    return request({
        method: 'post',
        url: '/xe.marketing.drp.batch_change_customer_member/1.0.0',
        params,
        requestType: 'json',
        pack : ''
    });
}

// C端操作会员积分
export const operatePoint = (params) => {
    return request({
        method: 'post',
        url: '/xe.marketing.drp.distribute.point.save/1.0.0',
        params,
        requestType: 'json',
        pack : ''
    });
};
// C端批量操作会员积分
export const batchOperatePoint = (params) => {
    return request({
        method: 'post',
        url: '/xe.marketing.drp.distribute.point.batch_save/1.0.0',
        params,
        requestType: 'json',
        pack : ''
    });
};

// 获取2.1菜单门店 O端灰度
export const getRetailGrayConfig = () => {
    return request({
        method: 'get',
        url: '/xe.marketing.drp.team.check_in_gray/1.0.0',
        params: {},
        requestType: 'json',
        pack : ''
    });
}

// 手机号解密
export const getUserBindPhone = (params) => {
    return request({
        method: 'post',
        url: '/xe.marketing.drp.get_user_bind_phone/1.0.0',
        params,
        requestType: 'json',
        pack : ''
    });
};

// 门店管理-获取等级会员样式
export const getRankStyle = (params) => {
    return request({
        method: 'post',
        url: '/xe.level-membership.customer.user/rank_style/2.0.0',
        params,
        requestType: 'json',
        pack : ''
    });
};

// 门店管理-会员调整-调整等级或成长值预览接口
export const getPreviewLevelChanging = (params) => {
    return request({
        method: 'post',
        url: '/xe.marketing.drp.level_membership/preview_level_changing/1.0.0',
        params,
        requestType: 'json',
        pack : ''
    });
};

// 门店管理-会员调整-设置等级和成长值接口
export const changeLevelOrScore = (params) => {
    return request({
        method: 'post',
        url: '/xe.marketing.drp.level_membership/change_level_or_score/1.0.0',
        params,
        requestType: 'json',
        pack : ''
    });
};

// 门店管理-会员调整-设置等级和成长值接口-批量
export const batchChangeLevelOrScore = (params) => {
    return request({
        method: 'post',
        url: '/xe.marketing.drp.level_membership/change_level_or_score.batch_change/1.0.0',
        params,
        requestType: 'json',
        pack : ''
    });
};

//微信初始化
export const wxInitData = (params) => {
    const pathArray = window.location.pathname.split('/');
    const pathType = pathArray[pathArray.length - 1];
    return request({
        url: '/xe.marketing.drp.distribute.get_wxinit_and_data/1.0.0',
        pack: '',
        params: {
            type: pathType,
            share_target_link: window.location.href,
            ...params
        },
        requestType: 'json'  // 新增参数
    });
};

export const getShareConf = (params) => {
    return request({
        url: 'xe.marketing.basic.custom.share.get/1.0.0',
        params,
        requestType: 'json'  // 新增参数
    });
};

// 门店管理-获取用户详情
export const getCustomerDetail = (params) => {
    return request({
        url: 'xe.marketing.drp.distribute_team_customers.data_overview/1.0.0',
        params,
        requestType: 'json'  // 新增参数
    });
};
// 设置黑名单 批量/单个
export const setBlankList = (params) => {
    return request({
        url: '/xe.marketing.drp.distribute.black_list.set/1.0.0',
        params,
        requestType: 'json'  // 新增参数
    });
};
//
// 新增跟进记录
export const addTraceRecords = (params) => {
    return request({
        url: '/xe.marketing.drp.store_customer/xe.retail_store_h5.trace_records.add/1.0.0',
        params,
        requestType: 'json'  // 新增参数
    });
};
// 获取跟进记录
export const getTraceRecords = (params) => {
    return request({
        url: '/xe.marketing.drp.store_customer/xe.retail_store_h5.trace_records.get/1.0.0',
        params,
        requestType: 'json'  // 新增参数
    });
};
// 红包领取记录
export const getRedEnvelopeLog = (params) => {
    return request({
        url: '/xe.marketing.drp.store_customer/xe.retail_store_h5.red_envelope_log.get/1.0.0',
        params,
        requestType: 'json'  // 新增参数
    });
};
// 看播记录
export const getAliveRecordsLog = (params) => {
    return request({
        url: '/xe.marketing.drp.store_customer/xe.retail_store_h5.alive_records.get/1.0.0',
        params,
        requestType: 'json'  // 新增参数
    });
};

// 获取礼品券列表
export const getGiftCouponList = (params) => {
    return request({
        url: 'xe.csg.coupon.gift_coupon.list/1.0.0',
        params,
        requestType: 'json'  // 新增参数
    });
};
// 获取核销列表
export const getWriteOffList = (params) => {
    return request({
        url: 'xe.ecommerce.ecommerce_tool.verification_code.applet_page.list/1.0.0',
        params,
        requestType: 'json'  // 新增参数
    });
}

// 获取是否在屏蔽积分余额灰度名单内
export const getDisPlayIntegralBalanceGrey = (params) => {
    return request({
        url: 'xe.point.gray.get/1.0.0',
        params,
        requestType: 'json'  // 新增参数
    });
}
/**
 * 获取积分明细，列表与详情共用接口，以业务id字段参数区分
 */
export const getPointDetail = (params) => {
    return request({
        url: '/xe.marketing.drp.point/xe.point.integral.detail/1.0.0',
        params,
        requestType: 'json'  // 新增参数
    });
    }


//获取优惠券数量
export const getCouponNum = (params) => {
    return request({
        url: 'xe.marketing.drp.personal_code.coupon_count_info/1.0.1',
        params,
        requestType: 'json'  // 新增参数
    });
}
//是否有礼品券灰度
export const getGiftCouponGray = (params) => {
    return request({
        url: 'xe.marketing.drp.personal_code.ecommerce.gms.category_o_gray/1.0.0',
        params,
        requestType: 'json'  // 新增参数
    });
}
// 获取优惠券列表数据
export const getCouponList = (params) => {
    return request({
        url: 'xe.marketing.drp.personal_code.coupon_list/1.0.1',
        params,
        requestType: 'json'  // 新增参数
    });
}
// 获取【冷备】优惠券数据
export const getCouponListBackup = (params) => {
    return request({
        url: 'xe.marketing.drp.personal_code.csg.coupon.cold_standby.user_list.get/1.0.0',
        params,
        requestType: 'json'  // 新增参数
    });
}

// 判断有无冷却数据
export const getIsColdStandBy = (params) => {
    return request({
        url: 'xe.marketing.drp.personal_code.coupon_cold_standby_check/1.0.1',
        params,
        requestType: 'json'  // 新增参数
    });
}
// 优惠券列表下获取礼品券列表
export const getGiftCouponDataList = (params) => {
    return request({
        url: 'xe.marketing.drp.personal_code.csg.coupon.user.list/1.0.1',
        params,
        requestType: 'json'  // 新增参数
    });
}

// 门店订单列表
export const getOrderList = (params) => {
    return request({
        url: '/xe.marketing.drp.store_customer/xe.marketing.drp.distribute_team_orders/1.0.1',
        params,
        requestType: 'json'  // 新增参数
    });
}
//灰度查询
export const checkAppInGray = (params) => {
    return request({
        url: '/xe.marketing.drp.check_app_in_gray/1.0.0',
        params,
        requestType: 'json'  // 新增参数
    });
}
