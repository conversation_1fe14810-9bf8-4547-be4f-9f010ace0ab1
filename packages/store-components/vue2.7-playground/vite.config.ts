/*
 * @Author: bate bat<PERSON><PERSON>@xiaoe-tech.com
 * @Date: 2025-07-04 11:51:35
 * @LastEditors: bate <EMAIL>
 * @LastEditTime: 2025-07-14 10:25:11
 * @FilePath: /h5-retail-shop-components/packages/store-components/vue2.7-playground/vite.config.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import vue2 from '@vitejs/plugin-vue2'
import autoprefixer from 'autoprefixer'
import { resolve } from 'path'
import { defineConfig } from 'vite'
import { defaultPlugins, entryFiles, entryLists, globalThisPatch, isCos } from '../vite.base.config'
const target = 'https://appegbzh7h16457.xet.citv.cn' // 代理访问店铺，本地测试模式下使用
const c_user_token = '82bbbb46e10984bc420d10f9f26d251d'
const realTarget = target.endsWith('/') ? target.substring(0, target.length - 1) : target
export default defineConfig(() => {
  return {
    plugins: [vue2(), ...defaultPlugins],
    server: {
      host: '0.0.0.0',
      port: 2700,
      cors: true,
      proxy: {
        '^(/xe)': {
          target, // 替换的服务器地址
          changeOrigin: true, // 开启代理，允许跨域
          headers: {
            cookie: `ko_token=${c_user_token}`,
            'x-forwarded-host': realTarget.includes('https://')
              ? realTarget.substr('https://'.length)
              : realTarget.substr('http://'.length),
            'x-forwarded-port': realTarget.includes('https://') ? '443' : '80'
          }
        }
      }
    },
    resolve: {
      alias: {
        '@': resolve(__dirname, '../src'),
        vue: resolve(__dirname, './node_modules/vue/dist/vue.runtime.esm.js'),
        'vue-demi': resolve(__dirname, '../node_modules/vue-demi/lib/v2.7/index.mjs'),
        dayjs: resolve(__dirname, '../node_modules/dayjs/esm/index.js'),
        'vant/es': resolve(__dirname, '../node_modules/vant-v2/es'),
        vant: resolve(__dirname, '../node_modules/vant-v2/es/index.js'),
        'vue-lazyload': resolve(__dirname, '../src/utils/lazyload/index.js')
      },
      extensions: ['.mjs', '.js', '.ts', '.json', '.vue', '.scss']
    },
    build: {
      outDir: resolve(__dirname, `../${isCos() ? 'libCos' : 'lib/v2.7'}`),
      emptyOutDir: false,
      target: 'es2015',
      lib: {
        entry: isCos() ? entryFiles('v2_7') : entryLists(),
        formats: isCos() ? ['umd'] : ['es'],
        name: process.env.MODULE_NAME || 'store-components',
        fileName: (format, entryName) => {
          return `${entryName}/index.js`
        }
      },
      rollupOptions: {
        external: ['vue'],
        output: {
          // 在 UMD 构建模式下为这些外部化的依赖提供一个全局变量
          globals: {
            vue: 'Vue'
          },
          assetFileNames: `[name]-[hash:6].[ext]`, //除了js以外资源
          banner: `${globalThisPatch}`
        }
      }
    },
    css: {
      postcss: {
        plugins: [
          autoprefixer({
            // 指定目标浏览器
            overrideBrowserslist: ['chrome >= 58', 'ie >= 11', 'safari >= 8', 'ios >= 8', 'android >= 8']
          })
        ]
      }
    },
    optimizeDeps: {
      exclude: ['vue-demi', 'vue']
    }
  }
})
