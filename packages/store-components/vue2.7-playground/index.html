<!--
 * @Author: bate <EMAIL>
 * @Date: 2025-05-06 10:21:27
 * @LastEditors: bate <EMAIL>
 * @LastEditTime: 2025-07-14 10:24:06
 * @FilePath: /h5-retail-shop-components/packages/store-components/vue2.7-playground/index.html
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>store-components</title>
    <script>
      window.__app_id = "appmtfpepbu6450";
      window.globVersionType = 4;
      // window.MEDIA_IFRAME_API_SERVER = 'https://iframe.test.xiaoeknow.com'
    </script>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/main.ts"></script>
    <style>
      html {
        font-size: 50px;
      }

      body {
        font-size: 50px;
      }
    </style> 
  </body>
</html>
