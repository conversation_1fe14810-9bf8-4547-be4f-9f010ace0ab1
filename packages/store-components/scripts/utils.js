const fs = require('fs')
const path = require('path')

function loadModule(name) {
  try {
    return require(name)
  }
  catch (e) {
    return undefined
  }
}

function copyFolderSync(source, target) {
  const _target = path.resolve(__dirname, target);
  if (!fs.existsSync(_target)) {
    fs.mkdirSync(_target);
  }

  source = path.resolve(__dirname, source);
  target = path.resolve(__dirname, target);

  const files = fs.readdirSync(source);

  files.forEach((file) => {
    const sourcePath = path.join(source, file);
    const targetPath = path.join(target, file);

    if (fs.statSync(sourcePath).isDirectory()) {
      copyFolderSync(sourcePath, targetPath);
    } else {
      fs.copyFileSync(sourcePath, targetPath);
    }
  });
}

function switchVersion(version) {
  try {
    const sourceFolder = `../lib/v${version}`
    const targetFolder = '../lib'
    copyFolderSync(sourceFolder, targetFolder)
    console.log(`use sense version: ${version}`)
  } catch {}
}

module.exports.loadModule = loadModule
module.exports.switchVersion = switchVersion
