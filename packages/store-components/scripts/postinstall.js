/*
 * @Author: bate bat<PERSON><PERSON>@xiaoe-tech.com
 * @Date: 2025-05-06 10:21:27
 * @LastEditors: bate <EMAIL>
 * @LastEditTime: 2025-05-06 10:24:47
 * @FilePath: /h5-retail-shop-components/packages/store-components/scripts/postinstall.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const { switchVersion, loadModule } = require("./utils");
const fs = require('fs');

const Vue = loadModule("vue");
const name = '@xiaoe/store-components';

// 组件库本地开发无需执行
if (fs.existsSync('./src')) {
  console.log(`${name}本地开发`);
} else if (!Vue || typeof Vue.version !== "string") {
  console.warn(`${name} Vue is not found. Please run "npm install vue" to install.`);
} else if (Vue.version.startsWith("3.")) {
  switchVersion("3");
} else if (Vue.version.startsWith('2.7.')) {
  switchVersion('2.7');
} else if (Vue.version.startsWith("2.")) {
  switchVersion("2");
} else {
  console.warn(
    `${name} Vue version v${Vue.version} is not supported.`
  );
}
