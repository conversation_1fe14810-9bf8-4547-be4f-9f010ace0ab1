{"semi": false, "tabWidth": 2, "singleQuote": true, "trailingComma": "none", "arrowParens": "avoid", "printWidth": 120, "bracketSpacing": true, "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "ignore", "proseWrap": "preserve", "useTabs": false, "quoteProps": "as-needed", "vueIndentScriptAndStyle": false, "jsxBracketSameLine": false, "jsxSingleQuote": false, "insertPragma": false, "requirePragma": false, "endOfLine": "auto"}