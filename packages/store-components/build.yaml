micro_component_conf:
  system_name: 'h5-retail-shop-components' #系统名称,按服务管理的名称
  docker_image: 'node:18.18.0-slim' #镜像名称，可在https://hub.docker.com/_/node/tags地址搜索对应版本的 node 镜像名称
  is_only_npm: false #是否只需要上传 cnpm，微组件则填 false
  build_command:
    - 'rm -rf node_modules/'
    - 'npm install -g pnpm@10.9.0 --prefix=${WORKSPACE}  --registry=https://registry.npmmirror.com' # 安装 cnpm最好指定 cnpm 版本，可能存在 node 版本与 cnpm 版本不兼容问题
    - '${WORKSPACE}/bin/pnpm -v'
    - '${WORKSPACE}/bin/pnpm config set registry=http://111.230.199.61:6888/'
    - '${WORKSPACE}/bin/pnpm i'
    - '${WORKSPACE}/bin/pnpm run build:cos'
