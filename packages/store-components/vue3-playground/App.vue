<!--
 * @Author: bate bateli<PERSON>@xiaoe-tech.com
 * @Date: 2025-07-01 17:22:32
 * @LastEditors: bate <EMAIL>
 * @LastEditTime: 2025-07-11 16:07:53
 * @FilePath: /h5-retail-shop-components/packages/store-components/vue3-playground/App.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <!-- <store-header right-text="更多" @getSelectTeam='getSelectTeam'></store-header> -->
    <selectTime></selectTime>

  </div>
</template>

<script setup>
import { ref } from 'vue'
import selectTime from '@/components/select-time/index.vue';

const getSelectTeam = (data)=>{
        console.log(data)
}


</script>

<style lang="scss"></style>
