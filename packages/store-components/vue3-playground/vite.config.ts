import vue from '@vitejs/plugin-vue'
import autoprefixer from 'autoprefixer'
import { resolve } from 'path'
import { defineConfig } from 'vite'
import { defaultPlugins, entryFiles, entryLists, globalThisPatch, isCos } from '../vite.base.config'
const target = 'https://appilvzothq7546.xet.citv.cn'; // 代理访问店铺，本地测试模式下使用
const c_user_token = '9b9f640f240911e275c14bedd5732130';
const realTarget = target.endsWith('/') ? target.substring(0, target.length - 1) : target;

export default defineConfig(() => {
  return {
    plugins: [vue(), ...defaultPlugins],
    server: {
      host: '0.0.0.0',
      port: 3000,
      cors: true,
      proxy: {
        '^(/scrm.clues.manage|/wework|/xe)': {
          target, // 替换的服务器地址
          changeOrigin: true, // 开启代理，允许跨域
          headers: {
            cookie: `ko_token=${c_user_token}`,
             'x-forwarded-host': realTarget.includes('https://')
             ? realTarget.substr('https://'.length)
             : realTarget.substr('http://'.length),
            'x-forwarded-port': realTarget.includes('https://') ? '443' : '80',
            // 'user-agent':'Mozilla/5.0 (iPhone; CPU iPhone OS 11_2_6 like Mac OS X) AppleWebKit/604.5.6 (KHTML, like Gecko) Mobile/15D100 MicroMessenger/7.0.1(0x17000120) NetType/WIFI Language/zh_CN'
          }
        }
      }
    },
    resolve: {
      alias: {
        '@': resolve(__dirname, '../src'),
        vue: resolve(__dirname, './node_modules/vue/dist/vue.runtime.esm-browser.js'),
        'vue-demi': resolve(__dirname, '../node_modules/vue-demi/lib/v3/index.mjs'),
        'dayjs': resolve(__dirname, '../node_modules/dayjs/esm/index.js'),
        "vant/es": resolve(__dirname, '../node_modules/vant-v3/es'),
        "vant": resolve(__dirname, '../node_modules/vant-v3/es/index.mjs')
      },
      extensions: ['.mjs', '.js', '.ts', '.json', '.vue', '.scss']
    },
    build: {
      outDir: resolve(__dirname, `../${isCos() ? 'libCos' : 'lib/v3'}`),
      emptyOutDir: false,
      target: 'es2015',
      lib: {
        entry: isCos() ? entryFiles('v3') : entryLists(),
        formats: isCos() ? ['umd'] : ['es'],
        name: process.env.MODULE_NAME || 'store-components', // umd模式需要设置不同的全局变量
        fileName: (format, entryName) => {
          return `${entryName}/index.js`
        }
      },
      rollupOptions: {
        external: ['vue'],
        output: {
          // 在 UMD 构建模式下为这些外部化的依赖提供一个全局变量
          globals: {
            vue: 'Vue3'
          },
          assetFileNames: `[name]-[hash:6].[ext]`, //除了js以外资源
          banner: `${globalThisPatch}`
        }
      }
    },
    css: {
      postcss: {
        plugins: [
          autoprefixer({
            // 指定目标浏览器
            overrideBrowserslist: ['chrome >= 58', 'ie >= 11', 'safari >= 8', 'ios >= 8', 'android >= 8']
          })
        ]
      }
    },
    optimizeDeps: {
      exclude: ['vue-demi', 'vue']
    }
  }
})
