import { libInjectCss } from "./plugins/libInjectCss.js";
import { fileURLToPath, URL } from "url";
import { relative } from "path";
import glob from "glob";
import requireTransform from "vite-plugin-require-transform";
import { viteStaticCopy } from "vite-plugin-static-copy";
import babel from "@rollup/plugin-babel";
import commonjs from "vite-plugin-commonjs";
import vitePluginImport from 'vite-plugin-babel-import';
// 分析包时开启注释
// import stats from "vite-plugin-stats-html";

export let defaultPlugins = [
  viteStaticCopy({
    targets: [
      {
        src: "../static",
        dest: "./",
      },
    ],
  }),
  libInjectCss({}),
  babel({
    presets: ["@babel/preset-env"],
    plugins: ["@babel/plugin-proposal-nullish-coalescing-operator"],
    babelHelpers: "bundled",
    exclude: "/node_modules/**",
  }),
  // 解决require引用问题
  requireTransform({
    fileRegex: /.js$|.vue$/,
  }),
  commonjs(),
  vitePluginImport([
    // {
    //   libraryName: '@xiaoe/h5-jump-fe',
    //   libraryDirectory: 'lib',
    //   style: false
    // },
  ]),
  // stats()
];

export const isCos = () => {
  return process.env.BUILD_MODE === "umd";
};

// 获取入口js
export const entryLists = () => {
  return Object.fromEntries(
    glob.sync("../libEntry/**/index.js").map((file) => {
      const key = `${relative('../libEntry', file.slice(0, file.length - 8)) || 'index'}`;
      return [
        key,
        fileURLToPath(new URL(file.slice(1), import.meta.url)),
      ];
    })
  );
};

// 获取入口js
export const entryList = (version) => {
  return Object.fromEntries(
    glob.sync("../libEntry/**/index.js").map((file) => {
      const key = `${relative('../libEntry', file.slice(0, file.length - 8)) || 'index'}-${version}`;
      return [
        key,
        fileURLToPath(new URL(file.slice(1), import.meta.url)),
      ];
    })
  );
};

export const entryFiles = (version) => {
  const { MODULE_NAME } = process.env;
  const entry_files = entryList(version);

  return {
    [MODULE_NAME]: entry_files[MODULE_NAME],
  };
};

export const globalThisPatch = `if (typeof globalThis === 'undefined') {window.globalThis = window;};`
