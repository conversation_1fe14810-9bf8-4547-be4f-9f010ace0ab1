/*
 * @Author: bate bat<PERSON><PERSON>@xiaoe-tech.com
 * @Date: 2025-05-06 10:21:27
 * @LastEditors: bate <EMAIL>
 * @LastEditTime: 2025-05-22 16:28:57
 * @FilePath: /h5-retail-shop-components/packages/store-components/libEntry/store-components/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import GoodsCard from '@/components/goods-card/index';
import IntegralDetail from '@/components/integral-detail/index';
import SelectTime from '@/components/select-time/index';
import { StoreHeader, permissionJump } from '@/components/store-components/index';
import UserCoupon from '@/components/user-coupon/index';
import { isVue2 } from 'vue-demi';
import packageInfo from "../../package.json";

StoreHeader.install = function (vue) {
  vue.component(StoreHeader.name, StoreHeader);
  if (isVue2) {
    vue.prototype.$storeHeader = vue.prototype.$storeHeader || storeHeader
  } else {
    app.config.globalProperties['$storeHeader'] = app.config.globalProperties['$storeHeader'] || storeHeader
  }
}
IntegralDetail.install = function (vue) {
  vue.component(IntegralDetail.name, IntegralDetail);
  if (isVue2) {
    vue.prototype.$integralDetail = vue.prototype.$integralDetail || IntegralDetail
  } else {
    app.config.globalProperties['$integralDetail'] = app.config.globalProperties['$integralDetail'] || IntegralDetail
  }
}
UserCoupon.install = function (vue) {
  vue.component(UserCoupon.name, UserCoupon);
  if (isVue2) {
    vue.prototype.$userCoupon = vue.prototype.$userCoupon || UserCoupon
  }
  else {
    app.config.globalProperties['$userCoupon'] = app.config.globalProperties['$userCoupon'] || UserCoupon
  }
}
GoodsCard.install = function (vue) {
  vue.component(GoodsCard.name, GoodsCard);
  if (isVue2) {
    vue.prototype.$goodsCard = vue.prototype.$goodsCard || GoodsCard
  }
  else {
    app.config.globalProperties['$goodsCard'] = app.config.globalProperties['$goodsCard'] || GoodsCard
  }
}

SelectTime.install = function (vue) {
  vue.component(SelectTime.name, SelectTime);
  if (isVue2) {
    vue.prototype.$selectTime = vue.prototype.$selectTime || SelectTime
  }
  else {
    app.config.globalProperties['$selectTime'] = app.config.globalProperties['$selectTime'] || SelectTime
  }
}

const { version } = packageInfo;
console.log(
  `%c @xioae/store-components %c v${version} `,
  "background:#35495e ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff",
  "background:#007ec6 ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff"
);

export { GoodsCard, IntegralDetail, SelectTime, StoreHeader, UserCoupon, permissionJump };

