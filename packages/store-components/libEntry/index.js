
import { GoodsCard, IntegralDetail, SelectTime, StoreHeader, UserCoupon, permissionJump } from './store-components';
const components = [ StoreHeader ];

const install = (Vue) => {
  components.forEach((component) => {
    component.name && Vue.component(component.name, component);
  });
};

/* istanbul ignore if */
if (typeof window !== "undefined" && window.Vue) {
  install(window.Vue);
}

export const COMPONENTS_LIST = {
    install,
    StoreHeader,
    permissionJump,
    IntegralDetail,
    UserCoupon,
    GoodsCard,
    SelectTime
};

export default COMPONENTS_LIST;
