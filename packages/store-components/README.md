## store-header 门店顶部选择器
### 引入方式 

```javascript
<template >
    <StoreHeader @getSelectTeam='getSelectTeam' @loaded="handlerComponentLoaded" />
</template>

import storeComponents from '@xiaoe/store-components';


export default {
  components: { StoreHeader:storeComponents.StoreHeader },
  data() {
    return {
      team_id: '',
    }
  },
  methods: {
    handlerComponentLoaded(item){
        this.team_id = item?.team_id;
    },
    getSelectTeam(item){
        this.team_id = item?.team_id;
    },
  }

}
```

### Options

| 参数名               | 类型              | 是否必填 | 默认值           | 描述                                            |
| 无                                        |

### 方法属性

| 属性名          | 类型    | 默认值 | 描述                                                           |
| --------------- | ------- | ------ | -------------------------------------------------------------- |
| getSelectTeam | func    |        | 切换门店后触发回调，返回当前选中的门店id和门店名称             | 
| loaded         | func    |        | 组件加载完成回调，返回当前选中的门店id和门店名称（有返回，无不返回）            | 


## permissionJump 门店权限跳转方法
### 引入方式 

```javascript
<template >
    
</template>
import storeComponents from '@xiaoe/store-components/lib/index';

export default {
  components: {  },
  data() {
    return {
      // 权限id自查表
      idMap: {
            AlivePromoterNodeId: 1, // 直播推广
            GoodsPromoterNodeId: 2, // 商品推广
            InviteCustomerNodeId: 3, // 邀请客户
            WriteOffNodeId: 4, // 核销
            GoodsStatNodeId: 5, // 商品统计
            OrderRecordNodeId: 6, // 订单记录
            ViewStoreAllOrderNodeId: 7, // 查看全店订单记录
            AfterSalesNodeId: 8, // 售后
            AchievementAccountNodeId: 9, // 业绩账户
            StoreAccountNodeId: 10, // 门店账户
            StoreJinXiaoCunNodeId: 11, // 门店进销存
            DistributePerformanceNodeId: 12, // 推广业绩（旧）
            StoreManageNodeId: 13, // 门店管理
            InviteStoreManagerNodeId: 14, // 邀请门店负责人
            SetPickupNodeId: 15, // 自提点设置
            InviteStaffNodeId: 16, // 邀请员
            InviteStoreCaptainNodeId: 17, // 邀请店长
            StaffManageNodeId: 18, // 员工管理
            AuditStaffApplyNodeId: 19, // 审核员工申请
            DownloadCenterNodeId: 20, // 下载中心
            StoreCustomerNodeId: 21, // 客户管理
            ViewStoreAllCustomerNodeId: 22, // 查看全店客户
            AssignCustomerNodeId: 23, // 分配客户
            AssignPointNodeId: 24, // 发放积分
            AdjustMembersNodeId: 25, // 调整会员
            ViewCustomerPhoneNodeId: 26, // 查看客户手机号
            LevelMembershipNodeId: 27, // 等级会员
            RedPocketSetNodeId: 28, // 红包设置
            CommunitySolitaireNodeId: 29, // 社区接龙
            AliveDataNodeId: 30, // 直播数据
            ViewStoreAllAliveDataNodeId: 31, // 查看全店直播数据
            DataStatisticsNodeId: 32, // 数据统计
            GiftCouponStatNodeId: 33, // 礼品券统计
            CouponStatNodeId: 34 // 优惠券统计
    }
  },
  mounted() {
    storeComponents.permissionJump('DataStatisticsNodeId');
    //需要拿到返回的状态
    storeComponents.permissionJump('DataStatisticsNodeId').then((res) => {
      console.log(res);
    }).catch((err) => {
      console.log(err);
    })

  },
  methods: {
  }

}
```

### Options

| 参数名               | 类型              | 是否必填 | 默认值           | 描述                                            |
| module_id           | string           | 是       | 无           | 权限节点id                                       |
| state               | boolean           | 否       | 无           |只返回权限状态，没有状态也不跳转 |  

# 版本更新
* 1.0.3 2025/3/17  
  1、xxxxxx
  merge_requests: xxxxxx
