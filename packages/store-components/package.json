{"name": "@xiaoe/store-components", "version": "1.0.16", "description": "门店顶部组件", "author": "<PERSON><PERSON>", "license": "ISC", "main": "lib/index/index.js", "maintainers": ["roamlu", "<PERSON><PERSON>"], "command": {"publish": {"registry": "http://**************:6888/"}}, "publishConfig": {"registry": "http://**************:6888/"}, "directories": {"lib": "lib", "test": "__tests__"}, "files": ["scripts", "lib"], "scripts": {"dev:3": "vue-demi-switch 3 vue3 && pnpm --filter vue3-playground dev", "dev:2.7": "vue-demi-switch 2.7 vue2 && pnpm --filter vue2.7-playground dev", "dev:2": "vue-demi-switch 2 vue2 && pnpm --filter vue2-playground dev", "build": "pnpm build:cos", "build:cos": "npm run clean && run-s build:2 build:2.7 build:3 build:cos2 build:cos2.7 build:cos3", "build:2": "vue-demi-switch 2 vue2 && pnpm --filter vue2-playground build", "build:2.7": "vue-demi-switch 2.7 vue2 && pnpm --filter vue2.7-playground build", "build:3": "vue-demi-switch 3 vue3 && pnpm --filter vue3-playground build", "build:cos2": "vue-demi-switch 2 vue2 && pnpm --filter vue2-playground build:cos", "build:cos2.7": "vue-demi-switch 2.7 vue2 && pnpm --filter vue2.7-playground build:cos", "build:cos3": "vue-demi-switch 3 vue3 && pnpm --filter vue3-playground build:cos", "postinstall": "node ./scripts/postinstall.js", "clean": "rimraf ./lib ./libCos", "build-dev:cos": "npm run clean && run-s build-dev:2 build-dev:2.7 build-dev:3 build-dev:cos2 build-dev:cos2.7 build-dev:cos3", "build-dev:2": "vue-demi-switch 2 vue2 && pnpm --filter vue2-playground build", "build-dev:2.7": "vue-demi-switch 2.7 vue2 && pnpm --filter vue2.7-playground build", "build-dev:3": "vue-demi-switch 3 vue3 && pnpm --filter vue3-playground build", "build-dev:cos2": "vue-demi-switch 2 vue2 && pnpm --filter vue2-playground build:cos", "build-dev:cos2.7": "vue-demi-switch 2.7 vue2 && pnpm --filter vue2.7-playground build:cos", "build-dev:cos3": "vue-demi-switch 3 vue3 && pnpm --filter vue3-playground build:cos"}, "dependencies": {"@wecom/jssdk": "^1.3.1", "@xiaoe/js-tools": "2.0.19", "@xiaoe/nvwa": "1.1.17", "@xiaoe/vant": "1.0.14", "axios": "^1.6.2", "dayjs": "1.11.13", "deepmerge": "^4.3.1", "dom-to-image": "^2.6.0", "glob": "7.1.2", "intersection-observer": "^0.12.2", "js-cookie": "^3.0.1", "magic-string": "^0.30.5", "pinyin-match": "^1.2.2", "qrcode": "^1.5.0", "vant-v2": "npm:vant@2.13.2", "vant-v3": "npm:vant@4.9.4", "vue-demi": "^0.14.5"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-runtime": "^7.18.10", "@babel/preset-env": "^7.13.10", "@rollup/plugin-babel": "^6.0.4", "@types/node": "^18.11.18", "@vitejs/plugin-legacy": "^4.1.1", "autoprefixer": "^10.4.16", "bumpp": "^8.2.1", "cross-env": "^7.0.3", "npm-run-all": "^4.1.5", "rimraf": "^3.0.2", "rollup-plugin-delete": "^2.0.0", "rollup-plugin-visualizer": "^5.9.2", "sass": "^1.57.1", "typescript": "~4.7.4", "vite": "^4.3.9", "vite-plugin-babel-import": "^2.0.5", "vite-plugin-commonjs": "^0.10.1", "vite-plugin-require-transform": "^1.0.21", "vite-plugin-static-copy": "^0.17.0", "vite-plugin-stats-html": "^1.1.0", "vue-tsc": "^0.40.13"}, "peerDependencies": {"@vue/composition-api": "^1.4.9", "vue": "^2.6.0 || >=3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "pnpm": {"packageExtensions": {"vue-template-compiler": {"peerDependencies": {"vue": "2.6.12"}}}}}