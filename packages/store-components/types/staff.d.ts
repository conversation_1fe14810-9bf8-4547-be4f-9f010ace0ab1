type DeptData = {
  dept_id: number,
  dept_order: number,
  extra_dept_id: string,
  dept_name: string,
  name: string,
  org_type: number,
  parent_id: number,
  nodeId: string | number,
  children: DeptData[],
  disabled?: boolean,
  reason?: string | string[],
  is_custom_disable?: boolean,
  checked?: boolean,
  is_deleted?: number, // 0-正常、1-删除、2-停用
  state?: number, // 搜索接口返回的数据，等同于is_deleted
}

type StaffData = {
  id: number,
  b_user_id: string,
  extra_user_id: string, //外部员工id
  extra_user_state: number, //外部员工状态：0-正常,1-离职
  role_name: string,
  name: string,
  org_type: number,
  avatar: string,
  is_pay: number, //0-免费 1-运营席位
  is_sale_pay: number, // 1-销售席位
  is_deleted: number, //0-正常、1-删除、2-停用
  licence_status: number, //激活码状态，1-绑定 2-未绑定
  first_install_free_period: string,
  dept_ids: number[],
  nodeId: string | number,
  disabled?: boolean,
  reason?: string | string[],
  is_custom_disable?: boolean,
  checked?: boolean,
  state?: number, // 搜索接口返回的数据，等同于is_deleted
}

type StaffTreeNode = DeptData | StaffData

type DisableUser = {
  userId: string
  reason: string
}

type StaffSelectorOptions = {
  title?: string
  defaultSelected?: string[]
  defaultSelectedDept?: string[]
  defaultSelectedExtra?: string[]
  disableUser?: DisableUser[]
  showInactive?: boolean
  limit?: {
    active: boolean
    licence: boolean
    salesSeat: boolean
    seatType: number
    operationSeat: boolean
    minSelect: number
    maxSelect: number
    isSelectOneType: boolean
  }
  extra?: {
    selectedCorpId: string
  }
  type?: String[]
  source?: string,
  treeRef: any
}

type StaffSelectorResult = {
  userList: UserList[]
  deptList: DeptList[]
  selectedData: StaffTreeNode[]
}


interface NodeMap {
  department: Record<any, StaffTreeNode>;
  user: Record<string, StaffTreeNode>;
  bUserId: Record<string, StaffTreeNode>;
  node: Record<string, StaffTreeNode>;
}


type NodeType = 'department' | 'user' | 'bUserId' | 'node'

type DBConfig = {
  name: string,
  version: number,
  objectStoreName: string,
  keyPath: string,
  appId: string,
}

type DBInstance = {
  name: string,
  db: IDBDatabase | null,
}

interface DeptList {
  id: number
  name: string
}

interface UserList {
  userId: string
  id: string
  name: string
  avatar: string
}