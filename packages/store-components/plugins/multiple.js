const path = require("path");
const glob = require("glob");
const { execSync } = require("child_process");

const modules = glob
  .sync("../libEntry/**/index.js")
  .map((file) => {
    const { BUILD_VERSION } = process.env;
    const key = `${path.relative('../libEntry', file.slice(0, file.length - 8)) || 'index'}-${BUILD_VERSION}`;
    return [key, path.resolve(__dirname, file)];
  })
  .map(([moduleName, modulePath]) => {
    return {
      name: moduleName,
      path: modulePath,
    };
  });

let task = [];
const createPromise = (name) => {
  return new Promise((resolve) => {
    execSync(
      // windows执行
      // `(set BUILD_MODE=umd) && (set MODULE_NAME=${name}) && vite build`,
      // coding执行
      `export BUILD_MODE=umd MODULE_NAME=${name} && vite build`,
      { stdio: "inherit" }
    );
    resolve(name);
  });
};

modules.forEach(({ name }, index) => {
  try {
    // 设置环境变量，然后运行 vite build
    task.push(createPromise(name));
  } catch (error) {
    console.error(error);
  }
});

Promise.all(task)
  .then((name) => {
    console.info(`${name} 成功`);
  })
  .catch((error) => {
    console.error(error);
  });
