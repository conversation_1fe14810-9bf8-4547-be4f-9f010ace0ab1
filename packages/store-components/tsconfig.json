{"compilerOptions": {"target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": false, "resolveJsonModule": true, "esModuleInterop": true, "lib": ["esnext", "dom", "dom.iterable", "scripthost"], "paths": {"@/*": ["./src/*"]}}, "include": ["src/**/*.vue", "src/**/*.ts", "env.d.ts", "playground/**/*.vue", "playground/**/*.ts", "vite.config.ts", "types/*.d.ts"], "exclude": ["node_modules", "dist"], "references": [{"path": "./tsconfig.node.json"}]}